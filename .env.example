# Environment Configuration Example
# Copy this file to .env and update with your actual values

# Application Environment
APP_ENV=development

# Required: OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
# For development (SQLite)
DEV_DATABASE_URL=sqlite:///./dev_database.db

# For production (PostgreSQL example)
# PROD_DATABASE_URL=postgresql://user:password@localhost:5432/saas_db

# For testing
# TEST_DATABASE_URL=sqlite:///./test_database.db

# Legacy database URI (for backward compatibility)
DB_URI=mysql+pymysql://user:password@localhost:3306/dev_db

# Database Pool Settings (for PostgreSQL/MySQL)
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Security
SECRET_KEY=your-secret-key-here-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=My SaaS Backend
API_TIMEOUT=30
MAX_QUERY_LENGTH=1000

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"]

# Logging
LOG_LEVEL=INFO
SQL_ECHO=false
DEBUG=True

# Email Configuration (optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Redis Configuration (optional, for caching)
REDIS_URL=redis://localhost:6379

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
UPLOAD_FOLDER=./uploads
