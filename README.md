# My SaaS Backend

A FastAPI-based backend service for AI-powered database query generation using natural language. This backend leverages LangChain and OpenAI to convert natural language queries into SQL and execute them against your database.

## Features

- **Natural Language to SQL**: Convert plain English queries to SQL using LangChain and OpenAI
- **FastAPI Framework**: High-performance, modern Python web framework
- **Database Integration**: Support for MySQL and other SQL databases
- **Chat Management**: RESTful API for managing chat sessions and messages
- **Docker Support**: Containerized deployment with Docker and Docker Compose
- **Development Tools**: Hot reload, database migrations with Alembic

## Technology Stack

- **Backend Framework**: FastAPI
- **AI/ML**: LangChain, OpenAI GPT models
- **Database**: MySQL (configurable)
- **ORM**: SQLAlchemy with Alembic migrations
- **Containerization**: Docker & Docker Compose
- **Environment Management**: python-dotenv

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start Guide](#quick-start-guide)
3. [Development Setup Options](#development-setup-options)
4. [Environment Configuration](#environment-configuration)
5. [Database Setup](#database-setup)
6. [API Documentation](#api-documentation)
7. [Testing](#testing)
8. [Deployment](#deployment)
9. [Troubleshooting](#troubleshooting)

---

## Prerequisites

Before you begin, ensure you have the following software installed on your system:

### Required Software

- **Git**: For version control
  - [Download Git](https://git-scm.com/downloads)
- **Docker Desktop**: For containerized development (Recommended)
  - [Download Docker Desktop](https://www.docker.com/products/docker-desktop)
- **Python 3.10+**: For local development (Alternative to Docker)
  - [Download Python](https://www.python.org/downloads/)

### API Keys Required

- **OpenAI API Key**: Required for natural language processing
  - Get your API key from [OpenAI Platform](https://platform.openai.com/api-keys)

## Quick Start Guide

### Option 1: Docker Development (Recommended)

This is the fastest way to get started with a complete development environment.

```bash
# 1. Clone the repository
git clone <your-repository-url>
cd My_SaaS_Backend

# 2. Create environment file
cp .env.example .env
# Edit .env file with your OpenAI API key

# 3. Start all services with Docker
docker-compose -f docker-compose.dev.yml up --build

# 4. Access the application
# API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Option 2: Local Python Development

For developers who prefer local Python development:

```bash
# 1. Clone the repository
git clone <your-repository-url>
cd My_SaaS_Backend

# 2. Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Create environment file
cp .env.example .env
# Edit .env file with your configuration

# 5. Start the application
uvicorn main:app --reload
```

## Development Setup Options

### Docker Development Setup (Recommended)

**Advantages:**
- Complete environment isolation
- Automatic database setup
- Consistent across all development machines
- No need to install Python dependencies locally

**Steps:**

1. **Clone and Navigate**:
   ```bash
   git clone <your-repository-url>
   cd My_SaaS_Backend
   ```

2. **Environment Configuration**:
   ```bash
   # Create environment file from template
   cp .env.example .env
   ```

   Edit `.env` file:
   ```env
   # Required: OpenAI API Key
   OPENAI_API_KEY=your_openai_api_key_here

   # Optional: Database Configuration (defaults provided)
   DB_URI=mysql+pymysql://user:password@db:3306/dev_db
   ```

3. **Start Development Environment**:
   ```bash
   # Build and start all services
   docker-compose -f docker-compose.dev.yml up --build

   # Or run in background
   docker-compose -f docker-compose.dev.yml up --build -d
   ```

4. **Access Your Application**:
   - **API**: http://localhost:8000
   - **Interactive API Docs**: http://localhost:8000/docs
   - **Alternative API Docs**: http://localhost:8000/redoc

5. **Stop Development Environment**:
   ```bash
   docker-compose -f docker-compose.dev.yml down
   ```

### Local Python Development Setup

**Advantages:**
- Direct access to Python environment
- Faster development cycle for Python-specific tasks
- Better IDE integration

**Steps:**

1. **Clone and Navigate**:
   ```bash
   git clone <your-repository-url>
   cd My_SaaS_Backend
   ```

2. **Python Environment Setup**:
   ```bash
   # Create virtual environment
   python3 -m venv venv

   # Activate virtual environment
   source venv/bin/activate  # On Windows: venv\Scripts\activate

   # Upgrade pip
   pip install --upgrade pip

   # Install dependencies
   pip install -r requirements.txt
   ```

3. **Environment Configuration**:
   ```bash
   cp .env.example .env
   ```

   Edit `.env` file with your configuration:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   # Add database configuration if using external database
   ```

4. **Start the Application**:
   ```bash
   # Start with auto-reload for development
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```


## Environment Configuration

### Environment Variables

Create a `.env` file in the root directory with the following configuration:

```env
# Required: OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Database Configuration (for local development)
DB_URI=mysql+pymysql://user:password@localhost:3306/dev_db

# Optional: Application Settings
DEBUG=True
LOG_LEVEL=INFO
```

### Environment Variables Description

- `OPENAI_API_KEY` (Required): Your OpenAI API key for natural language processing
- `DB_URI` (Optional): Database connection string. Docker setup provides this automatically
- `DEBUG` (Optional): Enable debug mode (default: False)
- `LOG_LEVEL` (Optional): Logging level (default: INFO)

### Creating .env.example Template

If `.env.example` doesn't exist, create it:

```bash
# Create template file
cat > .env.example << EOF
# Required: OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Database Configuration
DB_URI=mysql+pymysql://user:password@localhost:3306/dev_db

# Optional: Application Settings
DEBUG=True
LOG_LEVEL=INFO
EOF
```

## Database Setup

### Docker Database (Automatic)

When using Docker development setup, the database is automatically configured:

- **Database**: MySQL 8.0
- **Host**: localhost:3306
- **Database Name**: dev_db
- **Username**: user
- **Password**: password
- **Root Password**: root_password

### Local Database Setup

If you're running the application locally without Docker:

1. **Install MySQL**:
   ```bash
   # macOS with Homebrew
   brew install mysql

   # Ubuntu/Debian
   sudo apt-get install mysql-server

   # Windows: Download from https://dev.mysql.com/downloads/mysql/
   ```

2. **Create Database**:
   ```sql
   CREATE DATABASE dev_db;
   CREATE USER 'user'@'localhost' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON dev_db.* TO 'user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Update Environment**:
   ```env
   DB_URI=mysql+pymysql://user:password@localhost:3306/dev_db
   ```

### Database Migrations

The project uses Alembic for database migrations:

```bash
# Generate new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Downgrade migrations
alembic downgrade -1
```

## API Documentation

### Interactive API Documentation

Once your application is running, you can access the interactive API documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Main API Endpoints

#### Chat Management
```
GET    /chats              # Get user's chat history
POST   /chats              # Create new chat
GET    /chats/{id}         # Get specific chat with messages
PUT    /chats/{id}         # Update chat title
DELETE /chats/{id}         # Delete chat
POST   /chats/{id}/messages # Send message and get AI response
```

#### Database Integration
```
GET    /database/tables                    # Get available database tables
GET    /database/tables/{name}/schema      # Get table schema
POST   /database/query                     # Execute natural language query
```

#### Health Check
```
GET    /health             # Application health status
GET    /                   # Root endpoint
```

## Testing

### Running Tests with Docker

1. **Start test environment**:
   ```bash
   docker-compose -f docker-compose.dev.yml up --build -d
   ```

2. **Run tests inside container**:
   ```bash
   # Access container shell
   docker-compose -f docker-compose.dev.yml exec app bash

   # Run tests
   pytest

   # Run tests with coverage
   pytest --cov=.

   # Exit container
   exit
   ```

3. **Stop test environment**:
   ```bash
   docker-compose -f docker-compose.dev.yml down
   ```

### Running Tests Locally

```bash
# Activate virtual environment
source venv/bin/activate

# Install test dependencies
pip install pytest pytest-cov

# Run tests
pytest

# Run tests with coverage
pytest --cov=.

# Run specific test file
pytest tests/test_specific.py
```

### Test Structure

```
tests/
├── __init__.py
├── conftest.py          # Test configuration and fixtures
├── test_main.py         # Main application tests
├── test_query_agent.py  # Query agent tests
└── test_database.py     # Database integration tests
```

## Development Commands

### Docker Commands

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up --build

# Start in background
docker-compose -f docker-compose.dev.yml up --build -d

# Stop environment
docker-compose -f docker-compose.dev.yml down

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Rebuild specific service
docker-compose -f docker-compose.dev.yml build app

# Access container shell
docker-compose -f docker-compose.dev.yml exec app bash

# Run database migrations
docker-compose -f docker-compose.dev.yml exec app alembic upgrade head
```

### Local Development Commands

```bash
# Activate virtual environment
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Start application
uvicorn main:app --reload

# Run database migrations
alembic upgrade head

# Generate new migration
alembic revision --autogenerate -m "Description"

# Run tests
pytest

# Format code
black .

# Lint code
flake8 .
```

## Project Structure

```
My_SaaS_Backend/
├── alembic/                    # Database migrations
│   ├── versions/              # Migration files
│   ├── env.py                 # Alembic environment
│   └── script.py.mako         # Migration template
├── venv/                      # Virtual environment (local development)
├── __pycache__/               # Python cache files
├── main.py                    # FastAPI application entry point
├── query_agent.py             # LangChain query processing logic
├── requirements.txt           # Python dependencies
├── Dockerfile                 # Docker container configuration
├── docker-compose.dev.yml     # Development Docker Compose
├── docker-compose.prod.yml    # Production Docker Compose
├── alembic.ini               # Alembic configuration
├── .env.example              # Environment variables template
├── .env                      # Environment variables (create from template)
└── README.md                 # This file
```

## Deployment

### Production Deployment with Docker

1. **Prepare production environment**:
   ```bash
   # Create production environment file
   cp .env.example .env.prod

   # Edit with production values
   nano .env.prod
   ```

2. **Deploy with production compose**:
   ```bash
   docker-compose -f docker-compose.prod.yml up --build -d
   ```

### Manual Production Deployment

1. **Server setup**:
   ```bash
   # Install Python and dependencies
   sudo apt update
   sudo apt install python3 python3-pip python3-venv nginx

   # Clone repository
   git clone <your-repo-url>
   cd My_SaaS_Backend

   # Create virtual environment
   python3 -m venv venv
   source venv/bin/activate

   # Install dependencies
   pip install -r requirements.txt
   pip install gunicorn
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

3. **Run with Gunicorn**:
   ```bash
   gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
   ```

### Environment-Specific Configurations

#### Development
- Debug mode enabled
- Auto-reload on code changes
- Detailed error messages
- Local database

#### Production
- Debug mode disabled
- Optimized for performance
- Error logging to files
- Production database
- HTTPS enabled
- Rate limiting

## Troubleshooting

### Common Issues

1. **Docker Issues**:
   ```bash
   # Docker not running
   # Solution: Start Docker Desktop

   # Port already in use
   docker-compose -f docker-compose.dev.yml down
   lsof -ti:8000 | xargs kill -9

   # Permission issues
   sudo chown -R $USER:$USER .
   ```

2. **Database Connection Issues**:
   ```bash
   # Check database container status
   docker-compose -f docker-compose.dev.yml ps

   # View database logs
   docker-compose -f docker-compose.dev.yml logs db

   # Reset database
   docker-compose -f docker-compose.dev.yml down -v
   docker-compose -f docker-compose.dev.yml up --build
   ```

3. **OpenAI API Issues**:
   ```bash
   # Verify API key is set
   echo $OPENAI_API_KEY

   # Check API key validity
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        https://api.openai.com/v1/models
   ```

4. **Python Environment Issues**:
   ```bash
   # Recreate virtual environment
   rm -rf venv
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

### Debugging

1. **Enable debug logging**:
   ```env
   DEBUG=True
   LOG_LEVEL=DEBUG
   ```

2. **View application logs**:
   ```bash
   # Docker logs
   docker-compose -f docker-compose.dev.yml logs -f app

   # Local logs
   tail -f app.log
   ```

3. **Database debugging**:
   ```bash
   # Connect to database
   docker-compose -f docker-compose.dev.yml exec db mysql -u user -p dev_db

   # View database tables
   SHOW TABLES;
   ```

## Contributing

1. **Fork the repository**
2. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes**
4. **Run tests**:
   ```bash
   pytest
   ```
5. **Commit your changes**:
   ```bash
   git commit -m "Add: your feature description"
   ```
6. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```
7. **Create a Pull Request**

### Code Style

- Follow PEP 8 Python style guide
- Use Black for code formatting
- Use type hints where appropriate
- Write docstrings for functions and classes
- Add tests for new features

## Support

If you encounter any issues:

1. Check the [Troubleshooting](#troubleshooting) section
2. Verify your environment variables are correctly set
3. Ensure Docker is running (for Docker setup)
4. Check the application logs for error messages
5. Verify your OpenAI API key is valid and has sufficient credits

## CORS Configuration

If you're building a frontend application that will connect to this backend, you'll need to configure CORS (Cross-Origin Resource Sharing):

```python
# Add to your main.py
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",    # React default
        "http://localhost:5173",    # Vite default
        "http://localhost:5174",    # Vite alternative
        "https://yourdomain.com",   # Production domain
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## API Usage Examples

### Using curl

```bash
# Health check
curl http://localhost:8000/health

# Get API documentation
curl http://localhost:8000/docs

# Example chat creation (adjust based on your actual endpoints)
curl -X POST http://localhost:8000/chats \
  -H "Content-Type: application/json" \
  -d '{"title": "New Chat"}'
```

### Using Python requests

```python
import requests

# Base URL
BASE_URL = "http://localhost:8000"

# Health check
response = requests.get(f"{BASE_URL}/health")
print(response.json())

# Example API call (adjust based on your actual endpoints)
response = requests.post(
    f"{BASE_URL}/chats",
    json={"title": "New Chat"}
)
print(response.json())
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
```