"""
Database configuration and session management.

This module handles:
- Database connection setup
- Session management
- Environment-based configuration
- Connection pooling
"""

import os
from typing import Generator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
class DatabaseConfig:
    """Database configuration class."""
    
    def __init__(self):
        self.environment = os.getenv("APP_ENV", "development").lower()
        self.database_urls = {
            "development": os.getenv("DEV_DATABASE_URL", "sqlite:///./dev_database.db"),
            "test": os.getenv("TEST_DATABASE_URL", "sqlite:///./test_database.db"),
            "production": os.getenv("PROD_DATABASE_URL", "sqlite:///./prod_database.db"),
        }
        
    def get_database_url(self) -> str:
        """Get database URL for current environment."""
        url = self.database_urls.get(self.environment)
        if not url:
            logger.warning(f"No database URL found for environment: {self.environment}")
            return self.database_urls["development"]
        return url
    
    def is_sqlite(self) -> bool:
        """Check if current database is SQLite."""
        return self.get_database_url().startswith("sqlite")
    
    def is_postgresql(self) -> bool:
        """Check if current database is PostgreSQL."""
        url = self.get_database_url()
        return url.startswith("postgresql") or url.startswith("postgres")
    
    def is_mysql(self) -> bool:
        """Check if current database is MySQL."""
        return self.get_database_url().startswith("mysql")


# Initialize database configuration
db_config = DatabaseConfig()

# Create engine with appropriate settings
def create_database_engine():
    """Create database engine with environment-specific settings."""
    database_url = db_config.get_database_url()
    
    if db_config.is_sqlite():
        # SQLite-specific settings
        engine = create_engine(
            database_url,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
            echo=os.getenv("SQL_ECHO", "false").lower() == "true"
        )
    else:
        # PostgreSQL/MySQL settings
        engine = create_engine(
            database_url,
            pool_size=int(os.getenv("DB_POOL_SIZE", "10")),
            max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "20")),
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections every hour
            echo=os.getenv("SQL_ECHO", "false").lower() == "true"
        )
    
    logger.info(f"Database engine created for {db_config.environment} environment")
    return engine


# Create engine and session factory
engine = create_database_engine()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Import models to ensure they're registered with Base
from models import Base

def create_tables():
    """Create all database tables."""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise


def drop_tables():
    """Drop all database tables (use with caution!)."""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Error dropping database tables: {e}")
        raise


def get_db() -> Generator[Session, None, None]:
    """
    Dependency to get database session.
    
    This function is used as a FastAPI dependency to provide
    database sessions to route handlers.
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def get_db_session() -> Session:
    """
    Get a database session for direct use.
    
    Note: Remember to close the session when done.
    """
    return SessionLocal()


class DatabaseManager:
    """Database manager for advanced operations."""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = SessionLocal
    
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            with self.engine.connect() as connection:
                connection.execute("SELECT 1")
            logger.info("Database connection test successful")
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def get_table_names(self) -> list:
        """Get list of all table names in the database."""
        try:
            metadata = MetaData()
            metadata.reflect(bind=self.engine)
            return list(metadata.tables.keys())
        except Exception as e:
            logger.error(f"Error getting table names: {e}")
            return []
    
    def execute_raw_sql(self, sql: str, params: dict = None):
        """
        Execute raw SQL query.
        
        Args:
            sql: SQL query string
            params: Query parameters
            
        Returns:
            Query result
        """
        try:
            with self.engine.connect() as connection:
                result = connection.execute(sql, params or {})
                return result.fetchall()
        except Exception as e:
            logger.error(f"Error executing raw SQL: {e}")
            raise
    
    def backup_database(self, backup_path: str = None):
        """
        Create database backup (SQLite only for now).
        
        Args:
            backup_path: Path for backup file
        """
        if not db_config.is_sqlite():
            raise NotImplementedError("Backup currently only supported for SQLite")
        
        import shutil
        from datetime import datetime
        
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup_{timestamp}.db"
        
        try:
            # Extract database path from URL
            db_path = db_config.get_database_url().replace("sqlite:///", "")
            shutil.copy2(db_path, backup_path)
            logger.info(f"Database backup created: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Error creating database backup: {e}")
            raise


# Initialize database manager
db_manager = DatabaseManager()

# Health check function
def check_database_health() -> dict:
    """
    Check database health and return status information.
    
    Returns:
        Dictionary with health status information
    """
    try:
        connection_ok = db_manager.test_connection()
        table_count = len(db_manager.get_table_names())
        
        return {
            "status": "healthy" if connection_ok else "unhealthy",
            "connection": "ok" if connection_ok else "failed",
            "environment": db_config.environment,
            "database_type": "sqlite" if db_config.is_sqlite() else "postgresql" if db_config.is_postgresql() else "mysql",
            "table_count": table_count,
            "url_configured": bool(db_config.get_database_url())
        }
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "environment": db_config.environment
        }
