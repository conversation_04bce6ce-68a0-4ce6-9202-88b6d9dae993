# Database Directory

This directory contains all database-related files for the Multi-Tenant SaaS Backend application.

## Directory Structure

```
db/
├── README.md                    # This file - overview of database structure
├── schema/                      # Database schema documentation and utilities
│   ├── README.md               # Schema documentation
│   ├── schema.sql              # Complete DDL schema definition
│   ├── database_utils.py       # Schema inspection utilities
│   ├── schema_report.md        # Auto-generated schema report
│   └── current_schema.sql      # Current database export
└── migrations/                  # Alembic migration files
    ├── README.md               # Migration documentation
    ├── alembic.ini             # Alembic configuration
    ├── env.py                  # Alembic environment setup
    ├── script.py.mako         # Migration template
    └── versions/               # Individual migration files
        ├── f94b84844a35_add_phone_column_to_tenants_and_tenant_.py
        ├── 2f1c0bdb3de9_create_admin_users_table_with_all_.py
        ├── 9ead6fcd8109_add_status_column_to_tenants_and_tenant_.py
        └── 34cbd575694b_change_id_columns_from_uuid_to_integer.py
```

## Quick Reference

### Current Database Schema

The application uses **SQLite** for development with **SQLAlchemy ORM** and **Alembic** for migrations.

#### Main Tables:
1. **tenants** - Company/organization information (INTEGER ID)
2. **tenant_users** - Users within tenant organizations (INTEGER ID)  
3. **admin_users** - System administrators (INTEGER ID)

#### Key Features:
- ✅ **Integer Primary Keys** for all main tables
- ✅ **Status Tracking** (active/inactive) for all entities
- ✅ **Phone Number Support** for tenants, users, and admins
- ✅ **Encrypted Passwords** for admin authentication
- ✅ **Audit Fields** (created_at, updated_at) on all tables
- ✅ **Foreign Key Relationships** with proper constraints
- ✅ **Indexed Columns** for performance

### Current Data:
- **2 Tenants**: Tech Solutions Inc., StartupCorp
- **3 Tenant Users**: Alice Johnson, Bob Smith, Charlie Brown
- **2 Admin Users**: System Administrator, Support Manager

## Common Tasks

### View Schema Documentation:
```bash
# Read comprehensive schema docs
cat db/schema/README.md

# View current schema report
cat db/schema/schema_report.md

# View raw SQL schema
cat db/schema/current_schema.sql
```

### Database Migrations:
```bash
# Check current migration status
alembic current

# View migration history
alembic history --verbose

# Apply latest migrations
alembic upgrade head

# Generate new migration
alembic revision --autogenerate -m "Description"
```

### Schema Inspection:
```bash
# Generate fresh schema documentation
python db/schema/database_utils.py

# Direct database inspection
sqlite3 dev_database.db ".schema"
sqlite3 dev_database.db "PRAGMA table_info(tenants);"
```

### Database Access:
```bash
# Open SQLite CLI
sqlite3 dev_database.db

# View all tables
sqlite3 dev_database.db ".tables"

# Export data
sqlite3 dev_database.db ".dump" > backup.sql
```

## Files Description

### Schema Files:
- **`schema/README.md`** - Comprehensive schema documentation with table descriptions, relationships, and sample data
- **`schema/schema.sql`** - Complete DDL statements to recreate the database from scratch
- **`schema/database_utils.py`** - Python utilities for schema inspection and documentation generation
- **`schema/schema_report.md`** - Auto-generated detailed schema report (updated by utils)
- **`schema/current_schema.sql`** - Current database export in SQL format (updated by utils)

### Migration Files:
- **`migrations/README.md`** - Migration history and management documentation
- **`migrations/alembic.ini`** - Alembic configuration file
- **`migrations/env.py`** - Alembic environment setup and model imports
- **`migrations/versions/`** - Individual migration files in chronological order

## Development Workflow

### Making Schema Changes:
1. **Modify Models**: Update `models.py` with your changes
2. **Generate Migration**: `alembic revision --autogenerate -m "Description"`
3. **Review Migration**: Check the generated migration file for accuracy
4. **Apply Migration**: `alembic upgrade head`
5. **Update Documentation**: Run `python db/schema/database_utils.py`
6. **Test Changes**: Verify the changes work as expected

### Production Deployment:
1. **Backup Database**: Always backup before migrations
2. **Test on Staging**: Apply migration to staging environment first
3. **Apply to Production**: `alembic upgrade head`
4. **Verify Integrity**: Check that data and relationships are intact

## Important Notes

### ⚠️ Migration History:
The latest migration (`34cbd575694b`) converted all ID columns from UUID strings to integers. This migration **drops and recreates all tables**, which means any existing data was lost during this conversion.

### 🔒 Security:
- Admin passwords are encrypted using bcrypt
- Database connection details should be kept secure
- Use environment variables for sensitive configuration

### 📊 Performance:
- All email columns are indexed for fast lookups
- Foreign key constraints ensure data integrity
- Consider adding indexes on frequently queried columns

### 🚀 Scaling:
- Current setup uses SQLite for development
- For production, consider PostgreSQL or MySQL
- Migration scripts are database-agnostic through SQLAlchemy
