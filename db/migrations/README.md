# Database Migrations

This directory contains all database migration files managed by Alembic.

## Migration History

### Migration Files (in chronological order):

1. **f94b84844a35_add_phone_column_to_tenants_and_tenant_.py**
   - **Date**: Initial migration
   - **Changes**: Added phone columns to tenants and tenant_users tables
   - **Description**: Extended tenant and user models with phone number support

2. **2f1c0bdb3de9_create_admin_users_table_with_all_.py**
   - **Date**: Second migration  
   - **Changes**: Created admin_users table
   - **Description**: Added system administrator functionality with encrypted passwords

3. **9ead6fcd8109_add_status_column_to_tenants_and_tenant_.py**
   - **Date**: Third migration
   - **Changes**: Added status columns and password column
   - **Description**: 
     - Added `status` column to tenants table (active/inactive)
     - Added `status` column to tenant_users table (active/inactive)  
     - Added `password` column to admin_users table (encrypted)

4. **34cbd575694b_change_id_columns_from_uuid_to_integer.py** *(CURRENT)*
   - **Date**: Latest migration
   - **Changes**: Changed all ID columns from UUID strings to integers
   - **Description**: 
     - Converted tenants.id from VARCHAR(36) to INTEGER AUTO_INCREMENT
     - Converted tenant_users.id from VARCHAR(36) to INTEGER AUTO_INCREMENT
     - Converted admin_users.id from VARCHAR(36) to INTEGER AUTO_INCREMENT
     - Updated foreign key tenant_users.tenant_id to INTEGER
     - **⚠️ WARNING**: This migration drops and recreates all tables, losing existing data

## Current Schema State

After all migrations, the database contains:

### Tables:
- **tenants**: Company/organization information with integer IDs
- **tenant_users**: Users within tenant organizations with integer IDs
- **admin_users**: System administrators with integer IDs and encrypted passwords

### Key Features:
- ✅ Integer primary keys for all main tables
- ✅ Status tracking (active/inactive) for all entities
- ✅ Phone number support for tenants, users, and admins
- ✅ Encrypted password authentication for admin users
- ✅ Proper foreign key relationships
- ✅ Indexed columns for performance
- ✅ Audit fields (created_at, updated_at) on all tables

## Running Migrations

### Check current migration status:
```bash
alembic current
```

### View migration history:
```bash
alembic history --verbose
```

### Upgrade to latest migration:
```bash
alembic upgrade head
```

### Downgrade to previous migration:
```bash
alembic downgrade -1
```

### Generate new migration:
```bash
alembic revision --autogenerate -m "Description of changes"
```

## Migration Commands

### Development Workflow:
1. Make changes to models in `models.py`
2. Generate migration: `alembic revision --autogenerate -m "Description"`
3. Review generated migration file
4. Apply migration: `alembic upgrade head`

### Production Deployment:
1. Backup database before migration
2. Test migration on staging environment
3. Apply migration: `alembic upgrade head`
4. Verify data integrity after migration

## Important Notes

### ⚠️ Data Loss Warning:
The migration `34cbd575694b` (UUID to Integer conversion) **drops and recreates all tables**. This was necessary because SQLite doesn't support changing column types with existing data.

### Migration Safety:
- Always backup your database before running migrations in production
- Test migrations on a copy of production data first
- The UUID to Integer migration will lose all existing data
- Future migrations should be designed to preserve data when possible

### File Structure:
```
db/migrations/
├── README.md                    # This file
├── alembic.ini                  # Alembic configuration
├── env.py                       # Alembic environment setup
├── script.py.mako              # Migration template
└── versions/                    # Migration files
    ├── f94b84844a35_add_phone_column_to_tenants_and_tenant_.py
    ├── 2f1c0bdb3de9_create_admin_users_table_with_all_.py
    ├── 9ead6fcd8109_add_status_column_to_tenants_and_tenant_.py
    └── 34cbd575694b_change_id_columns_from_uuid_to_integer.py
```

## Troubleshooting

### Common Issues:

1. **Migration conflicts**: If multiple developers create migrations simultaneously
   ```bash
   alembic merge heads -m "Merge migrations"
   ```

2. **Failed migration**: If a migration fails partway through
   ```bash
   alembic downgrade -1
   # Fix the issue, then
   alembic upgrade head
   ```

3. **Schema drift**: If manual changes were made to the database
   ```bash
   alembic revision --autogenerate -m "Sync schema changes"
   ```

### Best Practices:
- Always review auto-generated migrations before applying
- Use descriptive migration messages
- Test migrations on development data first
- Keep migrations small and focused
- Document breaking changes in migration comments
