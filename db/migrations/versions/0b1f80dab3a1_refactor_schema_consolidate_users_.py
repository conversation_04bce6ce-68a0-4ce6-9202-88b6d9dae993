"""Refactor schema: consolidate users, normalize chats, update api_keys

Revision ID: 0b1f80dab3a1
Revises: 34cbd575694b
Create Date: 2025-07-11 12:53:48.996541

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0b1f80dab3a1'
down_revision: Union[str, Sequence[str], None] = '34cbd575694b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 1. Add authentication fields to tenant_users
    op.add_column('tenant_users', sa.Column('hashed_password', sa.String(length=255), nullable=False))
    op.add_column('tenant_users', sa.Column('is_superuser', sa.<PERSON>an(), default=False))
    op.add_column('tenant_users', sa.Column('last_login', sa.DateTime(), nullable=True))
    op.create_unique_constraint('uq_tenant_users_email', 'tenant_users', ['email'])

    # 2. Update api_keys table
    op.drop_constraint('api_keys_user_id_fkey', 'api_keys', type_='foreignkey')
    op.drop_column('api_keys', 'user_id')
    op.add_column('api_keys', sa.Column('tenant_id', sa.Integer(), nullable=False))
    op.alter_column('api_keys', 'id', type_=sa.Integer(), postgresql_using='id::integer')
    op.drop_column('api_keys', 'is_active')
    op.add_column('api_keys', sa.Column('status', sa.String(length=20), nullable=False, server_default='active'))
    op.create_foreign_key('fk_api_keys_tenant_id', 'api_keys', 'tenants', ['tenant_id'], ['id'])

    # 3. Update chats table
    op.alter_column('chats', 'id', type_=sa.Integer(), postgresql_using='id::integer')
    op.drop_constraint('chats_user_id_fkey', 'chats', type_='foreignkey')
    op.drop_column('chats', 'user_id')
    op.add_column('chats', sa.Column('tenant_user_id', sa.Integer(), nullable=False))
    op.drop_column('chats', 'is_archived')
    op.drop_column('chats', 'is_shared')
    op.drop_column('chats', 'share_token')
    op.create_foreign_key('fk_chats_tenant_user_id', 'chats', 'tenant_users', ['tenant_user_id'], ['id'])

    # 4. Create new chat-related tables
    op.create_table('archived_chats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chat_id', sa.Integer(), nullable=False),
        sa.Column('tenant_user_id', sa.Integer(), nullable=False),
        sa.Column('archived_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('reason', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['chat_id'], ['chats.id']),
        sa.ForeignKeyConstraint(['tenant_user_id'], ['tenant_users.id']),
        sa.UniqueConstraint('chat_id')
    )

    op.create_table('shared_chats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chat_id', sa.Integer(), nullable=False),
        sa.Column('share_token', sa.String(length=255), nullable=False),
        sa.Column('tenant_user_id', sa.Integer(), nullable=False),
        sa.Column('shared_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('access_count', sa.Integer(), default=0),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['chat_id'], ['chats.id']),
        sa.ForeignKeyConstraint(['tenant_user_id'], ['tenant_users.id']),
        sa.UniqueConstraint('share_token'),
        sa.UniqueConstraint('chat_id')
    )

    op.create_table('deleted_chats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chat_id', sa.Integer(), nullable=False),
        sa.Column('original_title', sa.String(length=500), nullable=False),
        sa.Column('original_description', sa.Text(), nullable=True),
        sa.Column('tenant_user_id', sa.Integer(), nullable=False),
        sa.Column('database_connection_id', sa.String(length=36), nullable=True),
        sa.Column('deleted_by', sa.Integer(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('reason', sa.Text(), nullable=True),
        sa.Column('original_created_at', sa.DateTime(), nullable=True),
        sa.Column('original_updated_at', sa.DateTime(), nullable=True),
        sa.Column('original_settings', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['tenant_user_id'], ['tenant_users.id']),
        sa.ForeignKeyConstraint(['deleted_by'], ['tenant_users.id'])
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Reverse all the changes (this is complex and should be done carefully)
    op.drop_table('deleted_chats')
    op.drop_table('shared_chats')
    op.drop_table('archived_chats')

    # Restore chats table
    op.add_column('chats', sa.Column('share_token', sa.String(length=255), nullable=True))
    op.add_column('chats', sa.Column('is_shared', sa.Boolean(), nullable=True))
    op.add_column('chats', sa.Column('is_archived', sa.Boolean(), nullable=True))
    op.drop_constraint('fk_chats_tenant_user_id', 'chats', type_='foreignkey')
    op.drop_column('chats', 'tenant_user_id')
    op.add_column('chats', sa.Column('user_id', sa.String(length=36), nullable=False))
    op.alter_column('chats', 'id', type_=sa.String(length=36))

    # Restore api_keys table
    op.drop_constraint('fk_api_keys_tenant_id', 'api_keys', type_='foreignkey')
    op.drop_column('api_keys', 'status')
    op.add_column('api_keys', sa.Column('is_active', sa.Boolean(), nullable=True))
    op.alter_column('api_keys', 'id', type_=sa.String(length=36))
    op.drop_column('api_keys', 'tenant_id')
    op.add_column('api_keys', sa.Column('user_id', sa.String(length=36), nullable=False))

    # Remove tenant_users enhancements
    op.drop_constraint('uq_tenant_users_email', 'tenant_users', type_='unique')
    op.drop_column('tenant_users', 'last_login')
    op.drop_column('tenant_users', 'is_superuser')
    op.drop_column('tenant_users', 'hashed_password')
