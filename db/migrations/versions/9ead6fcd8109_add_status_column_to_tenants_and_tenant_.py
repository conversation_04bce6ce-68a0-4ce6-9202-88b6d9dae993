"""Add status column to tenants and tenant_users, add password column to admin_users

Revision ID: 9ead6fcd8109
Revises: 2f1c0bdb3de9
Create Date: 2025-07-11 02:24:24.310794

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9ead6fcd8109'
down_revision: Union[str, Sequence[str], None] = '2f1c0bdb3de9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add password column to admin_users with a default temporary password
    op.add_column('admin_users', sa.Column('password', sa.String(length=255), nullable=False, server_default='$2b$12$temp.password.hash.placeholder'))

    # Add status column to tenant_users with default 'active'
    op.add_column('tenant_users', sa.Column('status', sa.String(length=20), nullable=False, server_default='active'))

    # Add status column to tenants with default 'active'
    op.add_column('tenants', sa.Column('status', sa.String(length=20), nullable=False, server_default='active'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tenants', 'status')
    op.drop_column('tenant_users', 'status')
    op.drop_column('admin_users', 'password')
    # ### end Alembic commands ###
