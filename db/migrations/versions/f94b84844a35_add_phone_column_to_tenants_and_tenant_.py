"""Add phone column to tenants and tenant_users tables

Revision ID: f94b84844a35
Revises: 9dfa63396dd4
Create Date: 2025-07-11 01:38:25.504352

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f94b84844a35'
down_revision: Union[str, Sequence[str], None] = '9dfa63396dd4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenant_users', sa.Column('phone', sa.String(length=20), nullable=True))
    op.add_column('tenants', sa.Column('phone', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tenants', 'phone')
    op.drop_column('tenant_users', 'phone')
    # ### end Alembic commands ###
