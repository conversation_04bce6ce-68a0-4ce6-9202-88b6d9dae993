# Database Schema Documentation

## Overview
This document describes the database schema for the Multi-Tenant SaaS Backend application.

## Database Type
- **Engine**: SQLite (Development)
- **ORM**: SQLAlchemy
- **Migration Tool**: Alembic

## Tables

### 1. tenants
**Purpose**: Stores tenant/company information for multi-tenant architecture

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique tenant identifier |
| employer_name | VARCHAR(255) | NOT NULL, INDEXED | Company/organization name |
| size | VARCHAR(50) | NULLABLE | Company size (small, medium, large) |
| email | VARCHAR(255) | NOT NULL, INDEXED | Primary contact email |
| phone | VARCHAR(20) | NULLABLE | Contact phone number |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'active' | Tenant status (active, inactive) |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

**Indexes**:
- `ix_tenants_employer_name` on `employer_name`
- `ix_tenants_email` on `email`

**Sample Data**:
```sql
INSERT INTO tenants (employer_name, email, phone, size, status) VALUES
('Tech Solutions Inc.', '<EMAIL>', '******-100-2000', 'large', 'active'),
('StartupCorp', '<EMAIL>', '******-200-3000', 'small', 'active');
```

### 2. tenant_users
**Purpose**: Stores users within tenant organizations

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique user identifier |
| name | VARCHAR(255) | NOT NULL | User's full name |
| role | VARCHAR(50) | NOT NULL | User role (admin, analyst, consumer, viewer) |
| email | VARCHAR(255) | NOT NULL, INDEXED | User's email address |
| phone | VARCHAR(20) | NULLABLE | User's phone number |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'active' | User status (active, inactive) |
| tenant_id | INTEGER | NOT NULL, FOREIGN KEY | Reference to tenants.id |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

**Foreign Keys**:
- `tenant_id` → `tenants.id`

**Indexes**:
- `ix_tenant_users_email` on `email`

**Sample Data**:
```sql
INSERT INTO tenant_users (name, email, phone, role, status, tenant_id) VALUES
('Alice Johnson', '<EMAIL>', '******-100-2001', 'admin', 'active', 1),
('Bob Smith', '<EMAIL>', '******-100-2002', 'analyst', 'active', 1),
('Charlie Brown', '<EMAIL>', NULL, 'consumer', 'active', 2);
```

### 3. admin_users
**Purpose**: Stores system administrators who manage the platform

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique admin identifier |
| name | VARCHAR(255) | NOT NULL | Admin's full name |
| phone | VARCHAR(20) | NULLABLE | Admin's phone number |
| role | VARCHAR(50) | NOT NULL | Admin role (super_admin, admin, moderator) |
| email | VARCHAR(255) | NOT NULL, UNIQUE, INDEXED | Admin's email address |
| password | VARCHAR(255) | NOT NULL | Encrypted password (bcrypt) |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'active' | Admin status (active, inactive) |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

**Indexes**:
- `ix_admin_users_email` on `email`
- `sqlite_autoindex_admin_users_1` (UNIQUE) on `email`

**Sample Data**:
```sql
INSERT INTO admin_users (name, email, password, phone, role, status) VALUES
('System Administrator', '<EMAIL>', '$2b$12$...', '******-000-0001', 'super_admin', 'active'),
('Support Manager', '<EMAIL>', '$2b$12$...', '******-000-0002', 'admin', 'active');
```

## Relationships

```
tenants (1) ←→ (many) tenant_users
```

- One tenant can have many tenant users
- Each tenant user belongs to exactly one tenant
- Cascade delete: When a tenant is deleted, all associated tenant_users are deleted

## Data Types Reference

| SQLAlchemy Type | SQLite Type | Description |
|----------------|-------------|-------------|
| Integer | INTEGER | Auto-incrementing integers |
| String(n) | VARCHAR(n) | Variable-length strings |
| DateTime | DATETIME | Date and time values |
| Boolean | BOOLEAN | True/false values |

## Status Values

### Tenant Status
- `active`: Tenant is operational and can access the system
- `inactive`: Tenant is suspended or deactivated

### User Status  
- `active`: User can log in and access their tenant's resources
- `inactive`: User account is suspended

### Admin Status
- `active`: Admin can access the admin dashboard
- `inactive`: Admin access is suspended

## Role Values

### Tenant User Roles
- `admin`: Full access within their tenant
- `analyst`: Data analysis and reporting access
- `consumer`: Read-only access to data
- `viewer`: Limited viewing permissions

### Admin Roles
- `super_admin`: Full system access, can manage everything
- `admin`: Standard admin access, can manage tenants and users
- `moderator`: Limited admin access, monitoring and support

## Security Notes

- All passwords are encrypted using bcrypt
- Admin emails must be unique across the system
- Tenant user emails are indexed for fast lookups
- Foreign key constraints ensure data integrity
