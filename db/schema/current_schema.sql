-- Current Database Schema Export
-- Generated: 2025-07-11T10:43:46.408456

CREATE TABLE admin_users (
	id INTEGER NOT NULL, 
	name VARCHA<PERSON>(255) NOT NULL, 
	phone VARCHAR(20), 
	role VARCHAR(50) NOT NULL, 
	email VARCHAR(255) NOT NULL, 
	status VARCHAR(20) DEFAULT 'active' NOT NULL, 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	password VARCHAR(255) DEFAULT '$2b$12$temp.password.hash.placeholder' NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (email)
);

CREATE INDEX ix_admin_users_email ON admin_users (email);

CREATE TABLE api_keys (
	id VARCHAR(36) NOT NULL,
	tenant_user_id INTEGER NOT NULL,
	service_name VARCHAR(100) NOT NULL,
	key_name VARCHAR(255) NOT NULL,
	encrypted_key TEXT NOT NULL,
	is_active BOOLEAN,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	last_used DATETIME,
	usage_count INTEGER,
	monthly_usage JSON,
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id)
);

CREATE TABLE chats (
	id INTEGER NOT NULL,
	tenant_user_id INTEGER NOT NULL,
	database_connection_id VARCHAR(36),
	title VARCHAR(500) NOT NULL,
	description TEXT,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	settings JSON,
	PRIMARY KEY (id),
	FOREIGN KEY(database_connection_id) REFERENCES database_connections (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id)
);

CREATE TABLE archived_chats (
	id INTEGER NOT NULL,
	chat_id INTEGER NOT NULL,
	tenant_user_id INTEGER NOT NULL,
	archived_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	reason TEXT,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (id),
	FOREIGN KEY(chat_id) REFERENCES chats (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id),
	UNIQUE (chat_id)
);

CREATE TABLE shared_chats (
	id INTEGER NOT NULL,
	chat_id INTEGER NOT NULL,
	share_token VARCHAR(255) NOT NULL,
	tenant_user_id INTEGER NOT NULL,
	shared_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	expires_at DATETIME,
	is_active BOOLEAN DEFAULT TRUE,
	access_count INTEGER DEFAULT 0,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (id),
	FOREIGN KEY(chat_id) REFERENCES chats (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id),
	UNIQUE (share_token),
	UNIQUE (chat_id)
);

CREATE TABLE deleted_chats (
	id INTEGER NOT NULL,
	chat_id INTEGER NOT NULL,
	original_title VARCHAR(500) NOT NULL,
	original_description TEXT,
	tenant_user_id INTEGER NOT NULL,
	database_connection_id VARCHAR(36),
	deleted_by INTEGER NOT NULL,
	deleted_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	reason TEXT,
	original_created_at DATETIME,
	original_updated_at DATETIME,
	original_settings JSON,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id),
	FOREIGN KEY(deleted_by) REFERENCES tenant_users (id)
);

CREATE TABLE database_connections (
	id VARCHAR(36) NOT NULL,
	tenant_id INTEGER NOT NULL,
	name VARCHAR(255) NOT NULL,
	db_type VARCHAR(50) NOT NULL,
	host VARCHAR(255),
	port INTEGER,
	database_name VARCHAR(255) NOT NULL,
	username VARCHAR(255),
	password_encrypted TEXT,
	connection_string TEXT,
	status VARCHAR(20) DEFAULT 'active' NOT NULL,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	last_tested DATETIME,
	metadata_info JSON,
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_id) REFERENCES tenants (id),
	UNIQUE (tenant_id)
);

CREATE TABLE messages (
	id INTEGER NOT NULL,
	chat_id INTEGER NOT NULL,
	tenant_user_id INTEGER,
	role VARCHAR(20) NOT NULL,
	content TEXT NOT NULL,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	metadata_info JSON,
	PRIMARY KEY (id),
	FOREIGN KEY(chat_id) REFERENCES chats (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id)
);

CREATE TABLE query_history (
	id INTEGER NOT NULL,
	tenant_user_id INTEGER NOT NULL,
	database_connection_id VARCHAR(36),
	message_id INTEGER,
	chat_id INTEGER,
	natural_language_query TEXT NOT NULL,
	generated_sql TEXT,
	query_result JSON,
	execution_status VARCHAR(20) NOT NULL,
	execution_time INTEGER,
	rows_affected INTEGER,
	error_message TEXT,
	query_complexity VARCHAR(20),
	tables_involved JSON,
	query_source VARCHAR(20) DEFAULT 'direct' NOT NULL,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (id),
	FOREIGN KEY(database_connection_id) REFERENCES database_connections (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id),
	FOREIGN KEY(message_id) REFERENCES messages (id),
	FOREIGN KEY(chat_id) REFERENCES chats (id)
);

CREATE TABLE tenant_users (
	id INTEGER NOT NULL,
	name VARCHAR(255) NOT NULL,
	role VARCHAR(50) NOT NULL,
	email VARCHAR(255) NOT NULL,
	phone VARCHAR(20),
	status VARCHAR(20) DEFAULT 'active' NOT NULL,
	tenant_id INTEGER NOT NULL,
	hashed_password VARCHAR(255) NOT NULL,
	is_superuser BOOLEAN DEFAULT FALSE,
	last_login DATETIME,
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_id) REFERENCES tenants (id),
	UNIQUE (email)
);

CREATE INDEX ix_tenant_users_email ON tenant_users (email);

CREATE TABLE tenants (
	id INTEGER NOT NULL, 
	employer_name VARCHAR(255) NOT NULL, 
	size VARCHAR(50), 
	email VARCHAR(255) NOT NULL, 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	phone VARCHAR(20), 
	status VARCHAR(20) DEFAULT 'active' NOT NULL, 
	PRIMARY KEY (id)
);

CREATE INDEX ix_tenants_email ON tenants (email);
CREATE INDEX ix_tenants_employer_name ON tenants (employer_name);
