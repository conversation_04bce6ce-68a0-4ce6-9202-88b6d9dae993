-- Database Schema for Multi-Tenant SaaS Backend
-- Generated: 2025-01-11
-- Engine: SQLite with SQLAlchemy ORM

-- ============================================================================
-- Table: tenants
-- Purpose: Stores tenant/company information for multi-tenant architecture
-- ============================================================================

CREATE TABLE tenants (
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    employer_name VARCHAR(255) NOT NULL,
    size VARCHAR(50),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for tenants table
CREATE INDEX ix_tenants_employer_name ON tenants (employer_name);
CREATE INDEX ix_tenants_email ON tenants (email);

-- ============================================================================
-- Table: tenant_users  
-- Purpose: Stores users within tenant organizations
-- ============================================================================

CREATE TABLE tenant_users (
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    tenant_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(tenant_id) REFERENCES tenants (id)
);

-- Indexes for tenant_users table
CREATE INDEX ix_tenant_users_email ON tenant_users (email);

-- ============================================================================
-- Table: admin_users
-- Purpose: Stores system administrators who manage the platform
-- ============================================================================

CREATE TABLE admin_users (
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role VARCHAR(50) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for admin_users table
CREATE INDEX ix_admin_users_email ON admin_users (email);

-- ============================================================================
-- Sample Data Inserts
-- ============================================================================

-- Insert sample tenants
INSERT INTO tenants (employer_name, email, phone, size, status) VALUES
('Tech Solutions Inc.', '<EMAIL>', '******-100-2000', 'large', 'active'),
('StartupCorp', '<EMAIL>', '******-200-3000', 'small', 'active');

-- Insert sample tenant users
INSERT INTO tenant_users (name, email, phone, role, status, tenant_id) VALUES
('Alice Johnson', '<EMAIL>', '******-100-2001', 'admin', 'active', 1),
('Bob Smith', '<EMAIL>', '******-100-2002', 'analyst', 'active', 1),
('Charlie Brown', '<EMAIL>', NULL, 'consumer', 'active', 2);

-- Insert sample admin users (passwords are bcrypt hashed)
INSERT INTO admin_users (name, email, password, phone, role, status) VALUES
('System Administrator', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '******-000-0001', 'super_admin', 'active'),
('Support Manager', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '******-000-0002', 'admin', 'active');

-- ============================================================================
-- Views (Optional - for reporting and analytics)
-- ============================================================================

-- View: tenant_summary
-- Purpose: Summary information about tenants and their user counts
CREATE VIEW tenant_summary AS
SELECT 
    t.id,
    t.employer_name,
    t.email,
    t.phone,
    t.size,
    t.status,
    t.created_at,
    COUNT(tu.id) as user_count,
    COUNT(CASE WHEN tu.status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN tu.role = 'admin' THEN 1 END) as admin_users
FROM tenants t
LEFT JOIN tenant_users tu ON t.id = tu.tenant_id
GROUP BY t.id, t.employer_name, t.email, t.phone, t.size, t.status, t.created_at;

-- View: user_details
-- Purpose: Detailed user information with tenant details
CREATE VIEW user_details AS
SELECT 
    tu.id,
    tu.name,
    tu.email,
    tu.phone,
    tu.role,
    tu.status,
    tu.created_at,
    t.employer_name as tenant_name,
    t.size as tenant_size,
    t.status as tenant_status
FROM tenant_users tu
JOIN tenants t ON tu.tenant_id = t.id;

-- ============================================================================
-- Constraints and Triggers
-- ============================================================================

-- Trigger: Update updated_at timestamp on tenants
CREATE TRIGGER update_tenants_updated_at 
    AFTER UPDATE ON tenants
    FOR EACH ROW
BEGIN
    UPDATE tenants SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Trigger: Update updated_at timestamp on tenant_users
CREATE TRIGGER update_tenant_users_updated_at 
    AFTER UPDATE ON tenant_users
    FOR EACH ROW
BEGIN
    UPDATE tenant_users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Trigger: Update updated_at timestamp on admin_users
CREATE TRIGGER update_admin_users_updated_at 
    AFTER UPDATE ON admin_users
    FOR EACH ROW
BEGIN
    UPDATE admin_users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
