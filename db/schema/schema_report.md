# Database Schema Report
Generated: 2025-07-11T10:43:46.399990
Engine: sqlite:///./dev_database.db
Dialect: sqlite

## Table Summary

| Table | Columns | Rows | Primary Key | Foreign Keys |
|-------|---------|------|-------------|--------------|
| admin_users | 9 | 2 | id | 0 |
| api_keys | 11 | 0 | id | 1 |
| chats | 11 | 0 | id | 2 |
| database_connections | 16 | 0 | id | 1 |
| messages | 11 | 0 | id | 1 |
| query_history | 12 | 0 | id | 2 |
| tenant_users | 9 | 3 | id | 1 |
| tenants | 8 | 2 | id | 0 |
| users | 10 | 1 | id | 0 |

## Table: admin_users

**Row Count**: 2

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | INTEGER | No |  | No | Yes |
| name | VARCHAR(255) | No |  | No | No |
| phone | VARCHAR(20) | Yes |  | No | No |
| role | VARCHAR(50) | No |  | No | No |
| email | VARCHAR(255) | No |  | No | No |
| status | VARCHAR(20) | No | 'active' | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| password | VARCHAR(255) | No | '$2b$12$temp.password.hash.placeholder' | No | No |

### Indexes

- `ix_admin_users_email` on `email`

---

## Table: api_keys

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) | No |  | No | Yes |
| user_id | VARCHAR(36) | No |  | No | No |
| service_name | VARCHAR(100) | No |  | No | No |
| key_name | VARCHAR(255) | No |  | No | No |
| encrypted_key | TEXT | No |  | No | No |
| is_active | BOOLEAN | Yes |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| last_used | DATETIME | Yes |  | No | No |
| usage_count | INTEGER | Yes |  | No | No |
| monthly_usage | JSON | Yes |  | No | No |

### Foreign Keys

- `user_id` → `users.id`

---

## Table: chats

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) | No |  | No | Yes |
| user_id | VARCHAR(36) | No |  | No | No |
| database_connection_id | VARCHAR(36) | Yes |  | No | No |
| title | VARCHAR(500) | No |  | No | No |
| description | TEXT | Yes |  | No | No |
| is_archived | BOOLEAN | Yes |  | No | No |
| is_shared | BOOLEAN | Yes |  | No | No |
| share_token | VARCHAR(255) | Yes |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| settings | JSON | Yes |  | No | No |

### Foreign Keys

- `database_connection_id` → `database_connections.id`
- `user_id` → `users.id`

---

## Table: database_connections

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) | No |  | No | Yes |
| user_id | VARCHAR(36) | No |  | No | No |
| name | VARCHAR(255) | No |  | No | No |
| db_type | VARCHAR(50) | No |  | No | No |
| host | VARCHAR(255) | Yes |  | No | No |
| port | INTEGER | Yes |  | No | No |
| database_name | VARCHAR(255) | No |  | No | No |
| username | VARCHAR(255) | Yes |  | No | No |
| password_encrypted | TEXT | Yes |  | No | No |
| connection_string | TEXT | Yes |  | No | No |
| is_default | BOOLEAN | Yes |  | No | No |
| is_active | BOOLEAN | Yes |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| last_tested | DATETIME | Yes |  | No | No |
| metadata_info | JSON | Yes |  | No | No |

### Foreign Keys

- `user_id` → `users.id`

---

## Table: messages

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) | No |  | No | Yes |
| chat_id | VARCHAR(36) | No |  | No | No |
| role | VARCHAR(20) | No |  | No | No |
| content | TEXT | No |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| sql_query | TEXT | Yes |  | No | No |
| query_result | JSON | Yes |  | No | No |
| execution_time | INTEGER | Yes |  | No | No |
| error_message | TEXT | Yes |  | No | No |
| metadata_info | JSON | Yes |  | No | No |

### Foreign Keys

- `chat_id` → `chats.id`

---

## Table: query_history

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) | No |  | No | Yes |
| user_id | VARCHAR(36) | No |  | No | No |
| database_connection_id | VARCHAR(36) | Yes |  | No | No |
| natural_language_query | TEXT | No |  | No | No |
| generated_sql | TEXT | Yes |  | No | No |
| execution_status | VARCHAR(20) | No |  | No | No |
| execution_time | INTEGER | Yes |  | No | No |
| rows_affected | INTEGER | Yes |  | No | No |
| error_message | TEXT | Yes |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| query_complexity | VARCHAR(20) | Yes |  | No | No |
| tables_involved | JSON | Yes |  | No | No |

### Foreign Keys

- `database_connection_id` → `database_connections.id`
- `user_id` → `users.id`

---

## Table: tenant_users

**Row Count**: 3

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | INTEGER | No |  | No | Yes |
| name | VARCHAR(255) | No |  | No | No |
| role | VARCHAR(50) | No |  | No | No |
| email | VARCHAR(255) | No |  | No | No |
| phone | VARCHAR(20) | Yes |  | No | No |
| status | VARCHAR(20) | No | 'active' | No | No |
| tenant_id | INTEGER | No |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |

### Foreign Keys

- `tenant_id` → `tenants.id`

### Indexes

- `ix_tenant_users_email` on `email`

---

## Table: tenants

**Row Count**: 2

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | INTEGER | No |  | No | Yes |
| employer_name | VARCHAR(255) | No |  | No | No |
| size | VARCHAR(50) | Yes |  | No | No |
| email | VARCHAR(255) | No |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| phone | VARCHAR(20) | Yes |  | No | No |
| status | VARCHAR(20) | No | 'active' | No | No |

### Indexes

- `ix_tenants_email` on `email`
- `ix_tenants_employer_name` on `employer_name`

---

## Table: users

**Row Count**: 1

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) | No |  | No | Yes |
| email | VARCHAR(255) | No |  | No | No |
| username | VARCHAR(100) | Yes |  | No | No |
| full_name | VARCHAR(255) | Yes |  | No | No |
| hashed_password | VARCHAR(255) | No |  | No | No |
| is_active | BOOLEAN | Yes |  | No | No |
| is_superuser | BOOLEAN | Yes |  | No | No |
| created_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| updated_at | DATETIME | Yes | CURRENT_TIMESTAMP | No | No |
| last_login | DATETIME | Yes |  | No | No |

### Indexes

- `ix_users_email` on `email` (UNIQUE)
- `ix_users_username` on `username` (UNIQUE)

---
