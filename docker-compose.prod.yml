version: "3.8"

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      # This DB_URI should be for your production database
      DB_URI: mysql+pymysql://user:password@your_production_db_host:3306/prod_db
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    # In production, you might want to use a pre-built image or a multi-stage build
    # to reduce image size and build time.
    # command: uvicorn main:app --host 0.0.0.0 --port 8000

volumes:
  db_data:
