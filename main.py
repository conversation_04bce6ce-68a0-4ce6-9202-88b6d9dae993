from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, RedirectResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from query_agent import run_nl_to_sql, get_database_tables, get_table_schema
import uuid
import re
from datetime import datetime

# Import database components
from database import get_db, check_database_health, create_tables
from models import User, Chat, Message, DatabaseConnection, Tenant, TenantUser, AdminUser
from crud import (
    get_user_crud, get_chat_crud, get_message_crud,
    get_db_connection_crud, get_query_history_crud,
    get_tenant_crud, get_tenant_user_crud, get_admin_user_crud
)

app = FastAPI(title="My SaaS Backend", description="Natural Language to SQL API with Database Integration")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database tables on startup
@app.on_event("startup")
async def startup_event():
    """Initialize database on startup."""
    try:
        create_tables()
        print("✅ Database tables initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        raise

# Data Models
class QueryRequest(BaseModel):
    db_uri: str  # e.g., mysql+pymysql://user:pass@host:3306/dbname
    question: str

class ChatCreate(BaseModel):
    title: str = "New Chat"

class ChatUpdate(BaseModel):
    title: Optional[str] = None
    archived: Optional[bool] = None

# Tenant Models
class TenantCreate(BaseModel):
    employer_name: str
    email: str
    phone: Optional[str] = None
    size: Optional[str] = None

class TenantUpdate(BaseModel):
    employer_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    size: Optional[str] = None

# Tenant User Models
class TenantUserCreate(BaseModel):
    name: str
    email: str
    phone: Optional[str] = None
    role: str
    tenant_id: int

class TenantUserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    role: Optional[str] = None
    tenant_id: Optional[str] = None
    title: Optional[str] = None
    archived: Optional[bool] = None

class MessageCreate(BaseModel):
    message: str
    role: str = "user"

class Message(BaseModel):
    id: str
    role: str  # "user" or "assistant"
    content: str
    created_at: datetime

class Chat(BaseModel):
    id: str
    title: str
    created_at: datetime
    updated_at: datetime
    archived: bool = False

class DatabaseQueryRequest(BaseModel):
    query: str
    selected_tables: List[str] = []

# Initialize CRUD instances
user_crud = get_user_crud()
chat_crud = get_chat_crud()
message_crud = get_message_crud()
db_connection_crud = get_db_connection_crud()
query_history_crud = get_query_history_crud()

# Default database URI for demo (you can change this)
DEFAULT_DB_URI = "mysql+pymysql://user:password@localhost:3306/dev_db"

# Chat Management Endpoints
@app.get("/chats")
def get_chats(include_archived: bool = False, db: Session = Depends(get_db)):
    """Get all chats, optionally including archived ones"""
    # For now, we'll use a default user ID. In production, this would come from authentication
    default_user_id = "default-user"

    # Create default user if not exists
    user = user_crud.get_user_by_id(db, default_user_id)
    if not user:
        user = user_crud.create_user(db, email="<EMAIL>", password="default", full_name="Default User")
        user.id = default_user_id
        db.commit()

    chats = chat_crud.get_user_chats(db, default_user_id, include_archived)
    return [{"id": chat.id, "title": chat.title, "created_at": chat.created_at,
             "updated_at": chat.updated_at, "archived": chat.is_archived} for chat in chats]

@app.post("/chats")
def create_chat(chat_data: ChatCreate):
    """Create a new chat"""
    chat_id = str(uuid.uuid4())
    now = datetime.now()

    chat = Chat(
        id=chat_id,
        title=chat_data.title,
        created_at=now,
        updated_at=now,
        archived=False
    )

    chats_db[chat_id] = chat
    messages_db[chat_id] = []

    return chat

@app.get("/chats/{chat_id}")
def get_chat(chat_id: str):
    """Get specific chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")
    return chats_db[chat_id]

@app.put("/chats/{chat_id}")
def update_chat(chat_id: str, chat_update: ChatUpdate):
    """Update chat title and/or archived status"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    if chat_update.title is not None:
        chats_db[chat_id].title = chat_update.title

    if chat_update.archived is not None:
        chats_db[chat_id].archived = chat_update.archived

    chats_db[chat_id].updated_at = datetime.now()

    return chats_db[chat_id]

@app.delete("/chats/{chat_id}")
def delete_chat(chat_id: str):
    """Delete chat permanently"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    del chats_db[chat_id]
    if chat_id in messages_db:
        del messages_db[chat_id]

    return {"message": "Chat deleted successfully"}

@app.post("/chats/{chat_id}/archive")
def archive_chat(chat_id: str):
    """Archive a chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    chats_db[chat_id].archived = True
    chats_db[chat_id].updated_at = datetime.now()

    return {"message": "Chat archived successfully", "chat": chats_db[chat_id]}

@app.post("/chats/{chat_id}/unarchive")
def unarchive_chat(chat_id: str):
    """Unarchive a chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    chats_db[chat_id].archived = False
    chats_db[chat_id].updated_at = datetime.now()

    return {"message": "Chat unarchived successfully", "chat": chats_db[chat_id]}

@app.get("/chats/archived")
def get_archived_chats():
    """Get all archived chats"""
    return [chat for chat in chats_db.values() if chat.archived]

@app.get("/chats/{chat_id}/messages")
def get_messages(chat_id: str):
    """Get messages for a chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    return messages_db.get(chat_id, [])

def is_greeting_or_welcome(message: str) -> bool:
    """Check if message is a greeting or welcome message"""
    message_lower = message.lower().strip()

    # Simple greetings - exact matches only
    simple_greetings = [
        'hi', 'hello', 'hey', 'there', 'hy', 'hii', 'helo',
        'good morning', 'good afternoon', 'good evening',
        'greetings', 'howdy', 'hi there', 'hello there'
    ]

    # Check for exact matches only
    if message_lower in simple_greetings:
        return True

    # Check for very specific greeting patterns (must be very short)
    if len(message_lower.split()) <= 2:
        greeting_patterns = [
            r'^(hi|hello|hey|hy|hii)[\s!.?]*$',
            r'^(hi|hello|hey)\s+(there|friend)[\s!.?]*$'
        ]

        for pattern in greeting_patterns:
            if re.match(pattern, message_lower):
                return True

    return False

def generate_welcome_response(chat_id: str) -> str:
    """Generate a contextual welcome response"""
    # Check if this is the first message in the chat
    chat_messages = messages_db.get(chat_id, [])
    user_messages = [msg for msg in chat_messages if msg.role == 'user']

    if len(user_messages) <= 1:  # First interaction
        return "Hi! 👋 How can I help you today?"
    else:  # Subsequent greetings in same chat
        return "Hello again! What would you like to know?"

@app.post("/chats/{chat_id}/messages")
def send_message(chat_id: str, message_data: MessageCreate):
    """Send message and get AI response"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    try:
        # Add user message
        user_message = Message(
            id=str(uuid.uuid4()),
            role="user",
            content=message_data.message,
            created_at=datetime.now()
        )

        if chat_id not in messages_db:
            messages_db[chat_id] = []

        messages_db[chat_id].append(user_message)

        # Check if this is a greeting/welcome message
        if is_greeting_or_welcome(message_data.message):
            assistant_content = generate_welcome_response(chat_id)
            sql_query = None
        else:
            # Generate SQL using LangChain
            sql_query = run_nl_to_sql(DEFAULT_DB_URI, message_data.message)
            assistant_content = f"Here's the SQL query for your request:\n\n```sql\n{sql_query}\n```\n\n💡 **Tip:** You can ask follow-up questions or request modifications to this query!"

        # Add assistant response
        assistant_message = Message(
            id=str(uuid.uuid4()),
            role="assistant",
            content=assistant_content,
            created_at=datetime.now()
        )

        messages_db[chat_id].append(assistant_message)

        # Update chat timestamp
        chats_db[chat_id].updated_at = datetime.now()

        return {
            "messages": [user_message, assistant_message],
            "sql_query": sql_query
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Database Integration Endpoints
@app.get("/database/tables")
def get_tables():
    """Get all available database tables"""
    try:
        tables = get_database_tables(DEFAULT_DB_URI)
        return tables
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tables: {str(e)}")

@app.get("/database/tables/{table_name}/schema")
def get_table_schema_endpoint(table_name: str):
    """Get schema for a specific table"""
    try:
        schema = get_table_schema(DEFAULT_DB_URI, table_name)
        return schema
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get table schema: {str(e)}")

@app.post("/database/query")
def execute_database_query(query_request: DatabaseQueryRequest):
    """Execute natural language query and return SQL + results"""
    try:
        sql_query = run_nl_to_sql(DEFAULT_DB_URI, query_request.query)

        return {
            "sql_query": sql_query,
            "natural_language_query": query_request.query,
            "selected_tables": query_request.selected_tables,
            "message": "SQL query generated successfully. Note: Actual execution is disabled for safety."
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Health check endpoint
@app.get("/")
def health_check():
    return {"status": "healthy", "message": "My SaaS Backend is running"}

# Original endpoint (keeping for backward compatibility)
@app.post("/query-text-to-sql/")
def query_text_to_sql(payload: QueryRequest):
    try:
        sql = run_nl_to_sql(payload.db_uri, payload.question)
        return {"sql_query": sql}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# TENANT MANAGEMENT ENDPOINTS
# ============================================================================

@app.get("/tenants", response_model=List[dict])
def get_tenants(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """Get all tenants with pagination"""
    try:
        tenant_crud = get_tenant_crud()
        tenants = tenant_crud.get_tenants(db, skip=skip, limit=limit)
        return [
            {
                "id": tenant.id,
                "employer_name": tenant.employer_name,
                "email": tenant.email,
                "phone": tenant.phone,
                "size": tenant.size,
                "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
                "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
            }
            for tenant in tenants
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenants: {str(e)}")

@app.post("/tenants", response_model=dict)
def create_tenant(tenant_data: TenantCreate, db: Session = Depends(get_db)):
    """Create a new tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant with email already exists
        existing_tenant = tenant_crud.get_tenant_by_email(db, tenant_data.email)
        if existing_tenant:
            raise HTTPException(status_code=400, detail="Tenant with this email already exists")

        tenant = tenant_crud.create_tenant(
            db=db,
            employer_name=tenant_data.employer_name,
            email=tenant_data.email,
            phone=tenant_data.phone,
            size=tenant_data.size
        )

        return {
            "id": tenant.id,
            "employer_name": tenant.employer_name,
            "email": tenant.email,
            "phone": tenant.phone,
            "size": tenant.size,
            "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
            "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create tenant: {str(e)}")

@app.get("/tenants/{tenant_id}", response_model=dict)
def get_tenant(tenant_id: int, db: Session = Depends(get_db)):
    """Get a specific tenant by ID"""
    try:
        tenant_crud = get_tenant_crud()
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        return {
            "id": tenant.id,
            "employer_name": tenant.employer_name,
            "email": tenant.email,
            "phone": tenant.phone,
            "size": tenant.size,
            "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
            "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant: {str(e)}")

@app.put("/tenants/{tenant_id}", response_model=dict)
def update_tenant(tenant_id: int, tenant_data: TenantUpdate, db: Session = Depends(get_db)):
    """Update a tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        existing_tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not existing_tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        # Check if email is being changed and if new email already exists
        if tenant_data.email and tenant_data.email != existing_tenant.email:
            email_exists = tenant_crud.get_tenant_by_email(db, tenant_data.email)
            if email_exists:
                raise HTTPException(status_code=400, detail="Tenant with this email already exists")

        # Update tenant
        update_data = {k: v for k, v in tenant_data.model_dump().items() if v is not None}
        tenant = tenant_crud.update_tenant(db, tenant_id, **update_data)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        return {
            "id": tenant.id,
            "employer_name": tenant.employer_name,
            "email": tenant.email,
            "phone": tenant.phone,
            "size": tenant.size,
            "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
            "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update tenant: {str(e)}")

@app.delete("/tenants/{tenant_id}")
def delete_tenant(tenant_id: int, db: Session = Depends(get_db)):
    """Delete a tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        # Delete tenant
        success = tenant_crud.delete_tenant(db, tenant_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete tenant")

        return {"message": "Tenant deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete tenant: {str(e)}")

# ============================================================================
# TENANT USER MANAGEMENT ENDPOINTS
# ============================================================================

@app.get("/tenant-users", response_model=List[dict])
def get_tenant_users(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """Get all tenant users with pagination"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        users = tenant_user_crud.get_tenant_users(db, skip=skip, limit=limit)
        return [
            {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "phone": user.phone,
                "role": user.role,
                "tenant_id": user.tenant_id,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            }
            for user in users
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant users: {str(e)}")

@app.get("/tenants/{tenant_id}/users", response_model=List[dict])
def get_tenant_users_by_tenant(tenant_id: int, db: Session = Depends(get_db)):
    """Get all users for a specific tenant"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id)
        return [
            {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "phone": user.phone,
                "role": user.role,
                "tenant_id": user.tenant_id,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            }
            for user in users
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant users: {str(e)}")

@app.post("/tenant-users", response_model=dict)
def create_tenant_user(user_data: TenantUserCreate, db: Session = Depends(get_db)):
    """Create a new tenant user"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        tenant = tenant_crud.get_tenant_by_id(db, user_data.tenant_id)
        if not tenant:
            raise HTTPException(status_code=400, detail="Tenant not found")

        # Check if user with email already exists
        existing_user = tenant_user_crud.get_tenant_user_by_email(db, user_data.email)
        if existing_user:
            raise HTTPException(status_code=400, detail="User with this email already exists")

        user = tenant_user_crud.create_tenant_user(
            db=db,
            name=user_data.name,
            email=user_data.email,
            phone=user_data.phone,
            role=user_data.role,
            tenant_id=user_data.tenant_id
        )

        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "role": user.role,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create tenant user: {str(e)}")

@app.get("/tenant-users/{user_id}", response_model=dict)
def get_tenant_user(user_id: int, db: Session = Depends(get_db)):
    """Get a specific tenant user by ID"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "role": user.role,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant user: {str(e)}")

@app.put("/tenant-users/{user_id}", response_model=dict)
def update_tenant_user(user_id: int, user_data: TenantUserUpdate, db: Session = Depends(get_db)):
    """Update a tenant user"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        tenant_crud = get_tenant_crud()

        # Check if user exists
        existing_user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not existing_user:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if tenant exists (if tenant_id is being updated)
        if user_data.tenant_id and user_data.tenant_id != existing_user.tenant_id:
            tenant = tenant_crud.get_tenant_by_id(db, user_data.tenant_id)
            if not tenant:
                raise HTTPException(status_code=400, detail="Tenant not found")

        # Check if email is being changed and if new email already exists
        if user_data.email and user_data.email != existing_user.email:
            email_exists = tenant_user_crud.get_tenant_user_by_email(db, user_data.email)
            if email_exists:
                raise HTTPException(status_code=400, detail="User with this email already exists")

        # Update user
        update_data = {k: v for k, v in user_data.model_dump().items() if v is not None}
        user = tenant_user_crud.update_tenant_user(db, user_id, **update_data)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "role": user.role,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update tenant user: {str(e)}")

@app.delete("/tenant-users/{user_id}")
def delete_tenant_user(user_id: int, db: Session = Depends(get_db)):
    """Delete a tenant user"""
    try:
        tenant_user_crud = get_tenant_user_crud()

        # Check if user exists
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Delete user
        success = tenant_user_crud.delete_tenant_user(db, user_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete user")

        return {"message": "User deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete tenant user: {str(e)}")

# ============================================================================
# ADMIN DASHBOARD ROUTES (HTML)
# ============================================================================

@app.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(request: Request, db: Session = Depends(get_db)):
    """Admin dashboard home page"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get stats
        tenants = tenant_crud.get_tenants(db)
        users = tenant_user_crud.get_tenant_users(db)

        stats = {
            'total_tenants': len(tenants),
            'total_users': len(users),
            'active_users': len([u for u in users if u.role in ['admin', 'analyst']]),
            'admin_users': len([u for u in users if u.role == 'admin'])
        }

        # Get recent tenants (last 5)
        recent_tenants = sorted(tenants, key=lambda x: x.created_at or datetime.min, reverse=True)[:5]

        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "stats": stats,
            "recent_tenants": recent_tenants,
            "current_time": datetime.now()
        })
    except Exception as e:
        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "stats": {'total_tenants': 0, 'total_users': 0, 'active_users': 0, 'admin_users': 0},
            "recent_tenants": [],
            "current_time": datetime.now(),
            "messages": [("error", f"Error loading dashboard: {str(e)}")]
        })

@app.get("/admin/tenants", response_class=HTMLResponse)
async def admin_tenants_list(request: Request, db: Session = Depends(get_db)):
    """Admin tenants list page"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get search and filter parameters
        search = request.query_params.get('search', '').strip()
        size_filter = request.query_params.get('size', '').strip()

        # Get all tenants
        tenants = tenant_crud.get_tenants(db)

        # Apply filters
        if search:
            tenants = [t for t in tenants if
                      search.lower() in t.employer_name.lower() or
                      search.lower() in t.email.lower()]

        if size_filter:
            tenants = [t for t in tenants if t.size == size_filter]

        # Add user count for each tenant
        for tenant in tenants:
            tenant.user_count = len(tenant_user_crud.get_tenant_users_by_tenant(db, tenant.id))

        return templates.TemplateResponse("tenants.html", {
            "request": request,
            "tenants": tenants
        })
    except Exception as e:
        return templates.TemplateResponse("tenants.html", {
            "request": request,
            "tenants": [],
            "messages": [("error", f"Error loading tenants: {str(e)}")]
        })

@app.get("/admin/tenants/new", response_class=HTMLResponse)
async def admin_tenant_create_form(request: Request):
    """Admin create tenant form"""
    return templates.TemplateResponse("tenant_form.html", {
        "request": request,
        "tenant": None,
        "form_data": {},
        "errors": {}
    })

@app.post("/admin/tenants/new", response_class=HTMLResponse)
async def admin_tenant_create(
    request: Request,
    db: Session = Depends(get_db),
    employer_name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    size: str = Form("")
):
    """Admin create tenant"""
    form_data = {
        'employer_name': employer_name,
        'email': email,
        'phone': phone,
        'size': size
    }
    errors = {}

    try:
        # Validate
        if not employer_name.strip():
            errors['employer_name'] = 'Company name is required'
        if not email.strip():
            errors['email'] = 'Email is required'
        elif '@' not in email:
            errors['email'] = 'Invalid email format'

        if errors:
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": None,
                "form_data": form_data,
                "errors": errors
            })

        # Create tenant
        tenant_crud = get_tenant_crud()

        # Check if email exists
        existing = tenant_crud.get_tenant_by_email(db, email)
        if existing:
            errors['email'] = 'A tenant with this email already exists'
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": None,
                "form_data": form_data,
                "errors": errors
            })

        tenant = tenant_crud.create_tenant(
            db=db,
            employer_name=employer_name,
            email=email,
            phone=phone if phone else None,
            size=size if size else None
        )

        return RedirectResponse(url=f"/admin/tenants/{tenant.id}?created=1", status_code=303)

    except Exception as e:
        return templates.TemplateResponse("tenant_form.html", {
            "request": request,
            "tenant": None,
            "form_data": form_data,
            "errors": {"general": f"Error creating tenant: {str(e)}"}
        })

@app.get("/admin/tenants/{tenant_id}", response_class=HTMLResponse)
async def admin_tenant_detail(request: Request, tenant_id: int, db: Session = Depends(get_db)):
    """Admin tenant detail/edit form"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": None,
                "form_data": {},
                "errors": {"general": "Tenant not found"}
            })

        # Get tenant users
        tenant_users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id)

        messages = []
        if request.query_params.get('created'):
            messages.append(("success", "Tenant created successfully!"))
        if request.query_params.get('updated'):
            messages.append(("success", "Tenant updated successfully!"))

        return templates.TemplateResponse("tenant_form.html", {
            "request": request,
            "tenant": tenant,
            "tenant_users": tenant_users,
            "form_data": {},
            "errors": {},
            "messages": messages
        })
    except Exception as e:
        return templates.TemplateResponse("tenant_form.html", {
            "request": request,
            "tenant": None,
            "form_data": {},
            "errors": {"general": f"Error loading tenant: {str(e)}"}
        })

@app.get("/admin/tenants/{tenant_id}/edit", response_class=HTMLResponse)
async def admin_tenant_edit_form(request: Request, tenant_id: int, db: Session = Depends(get_db)):
    """Admin edit tenant form"""
    return await admin_tenant_detail(request, tenant_id, db)

@app.post("/admin/tenants/{tenant_id}/edit", response_class=HTMLResponse)
async def admin_tenant_update(
    request: Request,
    tenant_id: int,
    db: Session = Depends(get_db),
    employer_name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    size: str = Form("")
):
    """Admin update tenant"""
    form_data = {
        'employer_name': employer_name,
        'email': email,
        'phone': phone,
        'size': size
    }
    errors = {}

    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get existing tenant
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": None,
                "form_data": form_data,
                "errors": {"general": "Tenant not found"}
            })

        # Validate
        if not employer_name.strip():
            errors['employer_name'] = 'Company name is required'
        if not email.strip():
            errors['email'] = 'Email is required'
        elif '@' not in email:
            errors['email'] = 'Invalid email format'

        # Check if email exists (but not for current tenant)
        if email != tenant.email:
            existing = tenant_crud.get_tenant_by_email(db, email)
            if existing:
                errors['email'] = 'A tenant with this email already exists'

        if errors:
            tenant_users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id)
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": tenant,
                "tenant_users": tenant_users,
                "form_data": form_data,
                "errors": errors
            })

        # Update tenant
        update_data = {
            'employer_name': employer_name,
            'email': email,
            'phone': phone if phone else None,
            'size': size if size else None
        }

        tenant_crud.update_tenant(db, tenant_id, **update_data)

        return RedirectResponse(url=f"/admin/tenants/{tenant_id}?updated=1", status_code=303)

    except Exception as e:
        tenant_users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id) if tenant else []
        return templates.TemplateResponse("tenant_form.html", {
            "request": request,
            "tenant": tenant,
            "tenant_users": tenant_users,
            "form_data": form_data,
            "errors": {"general": f"Error updating tenant: {str(e)}"}
        })

@app.post("/admin/tenants/{tenant_id}/delete")
async def admin_tenant_delete(tenant_id: int, db: Session = Depends(get_db)):
    """Admin delete tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            return RedirectResponse(url="/admin/tenants?error=Tenant not found", status_code=303)

        # Delete tenant
        success = tenant_crud.delete_tenant(db, tenant_id)
        if success:
            return RedirectResponse(url="/admin/tenants?deleted=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/tenants?error=Failed to delete tenant", status_code=303)

    except Exception as e:
        return RedirectResponse(url=f"/admin/tenants?error=Error deleting tenant: {str(e)}", status_code=303)

# ============================================================================
# ADMIN USER MANAGEMENT ROUTES
# ============================================================================

@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users_list(request: Request, db: Session = Depends(get_db)):
    """Admin users list page"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get search and filter parameters
        search = request.query_params.get('search', '').strip()
        tenant_filter = request.query_params.get('tenant_id', '').strip()
        role_filter = request.query_params.get('role', '').strip()

        # Get all users and tenants
        users = tenant_user_crud.get_tenant_users(db)
        all_tenants = tenant_crud.get_tenants(db)

        # Create tenant lookup
        tenant_lookup = {t.id: t.employer_name for t in all_tenants}

        # Apply filters
        if search:
            users = [u for u in users if
                    search.lower() in u.name.lower() or
                    search.lower() in u.email.lower() or
                    search.lower() in tenant_lookup.get(u.tenant_id, '').lower()]

        if tenant_filter:
            users = [u for u in users if u.tenant_id == tenant_filter]

        if role_filter:
            users = [u for u in users if u.role == role_filter]

        # Add tenant name to users
        for user in users:
            user.tenant_name = tenant_lookup.get(user.tenant_id, 'Unknown')

        messages = []
        if request.query_params.get('deleted'):
            messages.append(("success", "User deleted successfully!"))
        if request.query_params.get('error'):
            messages.append(("error", request.query_params.get('error')))

        return templates.TemplateResponse("users.html", {
            "request": request,
            "users": users,
            "all_tenants": all_tenants,
            "messages": messages
        })
    except Exception as e:
        return templates.TemplateResponse("users.html", {
            "request": request,
            "users": [],
            "all_tenants": [],
            "messages": [("error", f"Error loading users: {str(e)}")]
        })

@app.get("/admin/users/new", response_class=HTMLResponse)
async def admin_user_create_form(request: Request, db: Session = Depends(get_db)):
    """Admin create user form"""
    try:
        tenant_crud = get_tenant_crud()
        tenants = tenant_crud.get_tenants(db)

        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": None,
            "tenants": tenants,
            "form_data": {},
            "errors": {}
        })
    except Exception as e:
        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": None,
            "tenants": [],
            "form_data": {},
            "errors": {"general": f"Error loading form: {str(e)}"}
        })

@app.post("/admin/users/new", response_class=HTMLResponse)
async def admin_user_create(
    request: Request,
    db: Session = Depends(get_db),
    name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    role: str = Form(...),
    tenant_id: int = Form(...)
):
    """Admin create user"""
    form_data = {
        'name': name,
        'email': email,
        'phone': phone,
        'role': role,
        'tenant_id': tenant_id
    }
    errors = {}

    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()
        tenants = tenant_crud.get_tenants(db)

        # Validate
        if not name.strip():
            errors['name'] = 'Name is required'
        if not email.strip():
            errors['email'] = 'Email is required'
        elif '@' not in email:
            errors['email'] = 'Invalid email format'
        if not role:
            errors['role'] = 'Role is required'
        if not tenant_id:
            errors['tenant_id'] = 'Company is required'

        # Check if tenant exists
        if tenant_id:
            tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
            if not tenant:
                errors['tenant_id'] = 'Selected company does not exist'

        if errors:
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": None,
                "tenants": tenants,
                "form_data": form_data,
                "errors": errors
            })

        # Check if email exists
        existing = tenant_user_crud.get_tenant_user_by_email(db, email)
        if existing:
            errors['email'] = 'A user with this email already exists'
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": None,
                "tenants": tenants,
                "form_data": form_data,
                "errors": errors
            })

        # Create user
        user = tenant_user_crud.create_tenant_user(
            db=db,
            name=name,
            email=email,
            phone=phone if phone else None,
            role=role,
            tenant_id=tenant_id
        )

        return RedirectResponse(url=f"/admin/users?created=1", status_code=303)

    except Exception as e:
        tenants = tenant_crud.get_tenants(db) if 'tenant_crud' in locals() else []
        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": None,
            "tenants": tenants,
            "form_data": form_data,
            "errors": {"general": f"Error creating user: {str(e)}"}
        })

@app.get("/admin/users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_edit_form(request: Request, user_id: int, db: Session = Depends(get_db)):
    """Admin edit user form"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": None,
                "tenants": [],
                "form_data": {},
                "errors": {"general": "User not found"}
            })

        tenants = tenant_crud.get_tenants(db)

        messages = []
        if request.query_params.get('updated'):
            messages.append(("success", "User updated successfully!"))

        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": user,
            "tenants": tenants,
            "form_data": {},
            "errors": {},
            "messages": messages
        })
    except Exception as e:
        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": None,
            "tenants": [],
            "form_data": {},
            "errors": {"general": f"Error loading user: {str(e)}"}
        })

@app.post("/admin/users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_update(
    request: Request,
    user_id: int,
    db: Session = Depends(get_db),
    name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    role: str = Form(...),
    tenant_id: int = Form(...)
):
    """Admin update user"""
    form_data = {
        'name': name,
        'email': email,
        'phone': phone,
        'role': role,
        'tenant_id': tenant_id
    }
    errors = {}

    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get existing user
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": None,
                "tenants": [],
                "form_data": form_data,
                "errors": {"general": "User not found"}
            })

        tenants = tenant_crud.get_tenants(db)

        # Validate
        if not name.strip():
            errors['name'] = 'Name is required'
        if not email.strip():
            errors['email'] = 'Email is required'
        elif '@' not in email:
            errors['email'] = 'Invalid email format'
        if not role:
            errors['role'] = 'Role is required'
        if not tenant_id:
            errors['tenant_id'] = 'Company is required'

        # Check if tenant exists
        if tenant_id:
            tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
            if not tenant:
                errors['tenant_id'] = 'Selected company does not exist'

        # Check if email exists (but not for current user)
        if email != user.email:
            existing = tenant_user_crud.get_tenant_user_by_email(db, email)
            if existing:
                errors['email'] = 'A user with this email already exists'

        if errors:
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": user,
                "tenants": tenants,
                "form_data": form_data,
                "errors": errors
            })

        # Update user
        update_data = {
            'name': name,
            'email': email,
            'phone': phone if phone else None,
            'role': role,
            'tenant_id': tenant_id
        }

        tenant_user_crud.update_tenant_user(db, user_id, **update_data)

        return RedirectResponse(url=f"/admin/users/{user_id}/edit?updated=1", status_code=303)

    except Exception as e:
        tenants = tenant_crud.get_tenants(db) if 'tenant_crud' in locals() else []
        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": user if 'user' in locals() else None,
            "tenants": tenants,
            "form_data": form_data,
            "errors": {"general": f"Error updating user: {str(e)}"}
        })

@app.post("/admin/users/{user_id}/delete")
async def admin_user_delete(user_id: int, db: Session = Depends(get_db)):
    """Admin delete user"""
    try:
        tenant_user_crud = get_tenant_user_crud()

        # Check if user exists
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            return RedirectResponse(url="/admin/users?error=User not found", status_code=303)

        # Delete user
        success = tenant_user_crud.delete_tenant_user(db, user_id)
        if success:
            return RedirectResponse(url="/admin/users?deleted=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/users?error=Failed to delete user", status_code=303)

    except Exception as e:
        return RedirectResponse(url=f"/admin/users?error=Error deleting user: {str(e)}", status_code=303)


# Admin Users Management Routes
@app.get("/admin/admin-users", response_class=HTMLResponse)
async def admin_users_list(request: Request, db: Session = Depends(get_db)):
    """Display admin users list page."""
    admin_user_crud = get_admin_user_crud()

    # Get filter parameters
    search = request.query_params.get('search', '').strip()
    role_filter = request.query_params.get('role', '').strip()
    status_filter = request.query_params.get('status', '').strip()

    # Get all admin users
    admin_users = admin_user_crud.get_admin_users(db)

    # Apply filters
    if search:
        admin_users = [u for u in admin_users if
                      search.lower() in u.name.lower() or
                      search.lower() in u.email.lower()]

    if role_filter:
        admin_users = [u for u in admin_users if u.role == role_filter]

    if status_filter:
        admin_users = [u for u in admin_users if u.status == status_filter]

    return templates.TemplateResponse("admin_users.html", {
        "request": request,
        "admin_users": admin_users
    })


@app.get("/admin/admin-users/new", response_class=HTMLResponse)
async def admin_user_create_form(request: Request):
    """Display admin user creation form."""
    return templates.TemplateResponse("admin_user_form.html", {
        "request": request,
        "admin_user": None,
        "errors": {},
        "form_data": {}
    })


@app.post("/admin/admin-users/new", response_class=HTMLResponse)
async def admin_user_create(request: Request, db: Session = Depends(get_db)):
    """Handle admin user creation."""
    admin_user_crud = get_admin_user_crud()
    form_data = await request.form()
    errors = {}

    # Validate form data
    name = form_data.get('name', '').strip()
    email = form_data.get('email', '').strip()
    phone = form_data.get('phone', '').strip() or None
    role = form_data.get('role', '').strip()
    password = form_data.get('password', '').strip()
    confirm_password = form_data.get('confirm_password', '').strip()
    status = form_data.get('status', 'active').strip()

    if not name:
        errors['name'] = 'Name is required'

    if not email:
        errors['email'] = 'Email is required'
    elif admin_user_crud.get_admin_user_by_email(db, email):
        errors['email'] = 'Email already exists'

    if not role:
        errors['role'] = 'Role is required'

    if not password:
        errors['password'] = 'Password is required'
    elif len(password) < 8:
        errors['password'] = 'Password must be at least 8 characters'

    if password != confirm_password:
        errors['confirm_password'] = 'Passwords do not match'

    if errors:
        return templates.TemplateResponse("admin_user_form.html", {
            "request": request,
            "admin_user": None,
            "errors": errors,
            "form_data": form_data
        })

    try:
        admin_user_crud.create_admin_user(
            db=db,
            name=name,
            email=email,
            password=password,
            role=role,
            phone=phone,
            status=status
        )
        return RedirectResponse(url="/admin/admin-users", status_code=303)
    except Exception as e:
        errors['general'] = f'Error creating admin user: {str(e)}'
        return templates.TemplateResponse("admin_user_form.html", {
            "request": request,
            "admin_user": None,
            "errors": errors,
            "form_data": form_data
        })


@app.get("/admin/admin-users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_edit_form(request: Request, user_id: int, db: Session = Depends(get_db)):
    """Display admin user edit form."""
    admin_user_crud = get_admin_user_crud()
    admin_user = admin_user_crud.get_admin_user_by_id(db, user_id)

    if not admin_user:
        return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)

    return templates.TemplateResponse("admin_user_form.html", {
        "request": request,
        "admin_user": admin_user,
        "errors": {},
        "form_data": {}
    })


@app.post("/admin/admin-users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_edit(request: Request, user_id: int, db: Session = Depends(get_db)):
    """Handle admin user edit."""
    admin_user_crud = get_admin_user_crud()
    form_data = await request.form()
    errors = {}

    # Get existing admin user
    admin_user = admin_user_crud.get_admin_user_by_id(db, user_id)
    if not admin_user:
        return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)

    # Validate form data
    name = form_data.get('name', '').strip()
    email = form_data.get('email', '').strip()
    phone = form_data.get('phone', '').strip() or None
    role = form_data.get('role', '').strip()
    status = form_data.get('status', 'active').strip()
    new_password = form_data.get('new_password', '').strip()
    confirm_new_password = form_data.get('confirm_new_password', '').strip()

    if not name:
        errors['name'] = 'Name is required'

    if not email:
        errors['email'] = 'Email is required'
    elif email != admin_user.email and admin_user_crud.get_admin_user_by_email(db, email):
        errors['email'] = 'Email already exists'

    if not role:
        errors['role'] = 'Role is required'

    # Password validation (only if new password is provided)
    if new_password:
        if len(new_password) < 8:
            errors['new_password'] = 'Password must be at least 8 characters'
        elif new_password != confirm_new_password:
            errors['confirm_new_password'] = 'Passwords do not match'

    if errors:
        return templates.TemplateResponse("admin_user_form.html", {
            "request": request,
            "admin_user": admin_user,
            "errors": errors,
            "form_data": form_data
        })

    try:
        # Update admin user
        update_data = {
            'name': name,
            'email': email,
            'phone': phone,
            'role': role,
            'status': status
        }

        admin_user_crud.update_admin_user(db, user_id, **update_data)

        # Update password if provided
        if new_password:
            admin_user_crud.update_password(db, user_id, new_password)

        return RedirectResponse(url=f"/admin/admin-users/{user_id}/edit?updated=1", status_code=303)
    except Exception as e:
        errors['general'] = f'Error updating admin user: {str(e)}'
        return templates.TemplateResponse("admin_user_form.html", {
            "request": request,
            "admin_user": admin_user,
            "errors": errors,
            "form_data": form_data
        })


@app.post("/admin/admin-users/{user_id}/activate")
async def admin_user_activate(user_id: int, db: Session = Depends(get_db)):
    """Activate an admin user."""
    try:
        admin_user_crud = get_admin_user_crud()
        admin_user = admin_user_crud.activate_admin_user(db, user_id)

        if admin_user:
            return RedirectResponse(url="/admin/admin-users?activated=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)
    except Exception as e:
        return RedirectResponse(url=f"/admin/admin-users?error=Error activating admin user: {str(e)}", status_code=303)


@app.post("/admin/admin-users/{user_id}/deactivate")
async def admin_user_deactivate(user_id: int, db: Session = Depends(get_db)):
    """Deactivate an admin user."""
    try:
        admin_user_crud = get_admin_user_crud()
        admin_user = admin_user_crud.deactivate_admin_user(db, user_id)

        if admin_user:
            return RedirectResponse(url="/admin/admin-users?deactivated=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)
    except Exception as e:
        return RedirectResponse(url=f"/admin/admin-users?error=Error deactivating admin user: {str(e)}", status_code=303)


@app.post("/admin/admin-users/{user_id}/delete")
async def admin_user_delete(user_id: int, db: Session = Depends(get_db)):
    """Delete an admin user."""
    try:
        admin_user_crud = get_admin_user_crud()

        # Check if admin user exists
        admin_user = admin_user_crud.get_admin_user_by_id(db, user_id)
        if not admin_user:
            return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)

        # Delete admin user
        success = admin_user_crud.delete_admin_user(db, user_id)
        if success:
            return RedirectResponse(url="/admin/admin-users?deleted=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/admin-users?error=Failed to delete admin user", status_code=303)
    except Exception as e:
        return RedirectResponse(url=f"/admin/admin-users?error=Error deleting admin user: {str(e)}", status_code=303)
