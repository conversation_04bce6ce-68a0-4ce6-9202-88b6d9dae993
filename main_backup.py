from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from query_agent import run_nl_to_sql, get_database_tables, get_table_schema
import uuid
import re
from datetime import datetime

app = FastAPI(title="My SaaS Backend", description="Natural Language to SQL API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Data Models
class QueryRequest(BaseModel):
    db_uri: str  # e.g., mysql+pymysql://user:pass@host:3306/dbname
    question: str

class ChatCreate(BaseModel):
    title: str = "New Chat"

class ChatUpdate(BaseModel):
    title: Optional[str] = None
    archived: Optional[bool] = None

class MessageCreate(BaseModel):
    message: str
    role: str = "user"

class Message(BaseModel):
    id: str
    role: str  # "user" or "assistant"
    content: str
    created_at: datetime

class Chat(BaseModel):
    id: str
    title: str
    created_at: datetime
    updated_at: datetime
    archived: bool = False

class DatabaseQueryRequest(BaseModel):
    query: str
    selected_tables: List[str] = []

# In-memory storage (replace with real database in production)
chats_db: Dict[str, Chat] = {}
messages_db: Dict[str, List[Message]] = {}

# Default database URI for demo (you can change this)
DEFAULT_DB_URI = "mysql+pymysql://user:password@localhost:3306/dev_db"

# Chat Management Endpoints
@app.get("/chats")
def get_chats(include_archived: bool = False):
    """Get all chats, optionally including archived ones"""
    if include_archived:
        return list(chats_db.values())
    else:
        return [chat for chat in chats_db.values() if not chat.archived]

@app.post("/chats")
def create_chat(chat_data: ChatCreate):
    """Create a new chat"""
    chat_id = str(uuid.uuid4())
    now = datetime.now()

    chat = Chat(
        id=chat_id,
        title=chat_data.title,
        created_at=now,
        updated_at=now,
        archived=False
    )

    chats_db[chat_id] = chat
    messages_db[chat_id] = []

    return chat

@app.get("/chats/{chat_id}")
def get_chat(chat_id: str):
    """Get specific chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")
    return chats_db[chat_id]

@app.put("/chats/{chat_id}")
def update_chat(chat_id: str, chat_update: ChatUpdate):
    """Update chat title and/or archived status"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    if chat_update.title is not None:
        chats_db[chat_id].title = chat_update.title

    if chat_update.archived is not None:
        chats_db[chat_id].archived = chat_update.archived

    chats_db[chat_id].updated_at = datetime.now()

    return chats_db[chat_id]

@app.delete("/chats/{chat_id}")
def delete_chat(chat_id: str):
    """Delete chat permanently"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    del chats_db[chat_id]
    if chat_id in messages_db:
        del messages_db[chat_id]

    return {"message": "Chat deleted successfully"}

@app.post("/chats/{chat_id}/archive")
def archive_chat(chat_id: str):
    """Archive a chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    chats_db[chat_id].archived = True
    chats_db[chat_id].updated_at = datetime.now()

    return {"message": "Chat archived successfully", "chat": chats_db[chat_id]}

@app.post("/chats/{chat_id}/unarchive")
def unarchive_chat(chat_id: str):
    """Unarchive a chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    chats_db[chat_id].archived = False
    chats_db[chat_id].updated_at = datetime.now()

    return {"message": "Chat unarchived successfully", "chat": chats_db[chat_id]}

@app.get("/chats/archived")
def get_archived_chats():
    """Get all archived chats"""
    return [chat for chat in chats_db.values() if chat.archived]

@app.get("/chats/{chat_id}/messages")
def get_messages(chat_id: str):
    """Get messages for a chat"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    return messages_db.get(chat_id, [])

def is_greeting_or_welcome(message: str) -> bool:
    """Check if message is a greeting or welcome message"""
    message_lower = message.lower().strip()

    # Simple greetings - exact matches only
    simple_greetings = [
        'hi', 'hello', 'hey', 'there', 'hy', 'hii', 'helo',
        'good morning', 'good afternoon', 'good evening',
        'greetings', 'howdy', 'hi there', 'hello there'
    ]

    # Check for exact matches only
    if message_lower in simple_greetings:
        return True

    # Check for very specific greeting patterns (must be very short)
    if len(message_lower.split()) <= 2:
        greeting_patterns = [
            r'^(hi|hello|hey|hy|hii)[\s!.?]*$',
            r'^(hi|hello|hey)\s+(there|friend)[\s!.?]*$'
        ]

        for pattern in greeting_patterns:
            if re.match(pattern, message_lower):
                return True

    return False

def generate_welcome_response(chat_id: str) -> str:
    """Generate a contextual welcome response"""
    # Check if this is the first message in the chat
    chat_messages = messages_db.get(chat_id, [])
    user_messages = [msg for msg in chat_messages if msg.role == 'user']

    if len(user_messages) <= 1:  # First interaction
        return "Hi! 👋 How can I help you today?"
    else:  # Subsequent greetings in same chat
        return "Hello again! What would you like to know?"

@app.post("/chats/{chat_id}/messages")
def send_message(chat_id: str, message_data: MessageCreate):
    """Send message and get AI response"""
    if chat_id not in chats_db:
        raise HTTPException(status_code=404, detail="Chat not found")

    try:
        # Add user message
        user_message = Message(
            id=str(uuid.uuid4()),
            role="user",
            content=message_data.message,
            created_at=datetime.now()
        )

        if chat_id not in messages_db:
            messages_db[chat_id] = []

        messages_db[chat_id].append(user_message)

        # Check if this is a greeting/welcome message
        if is_greeting_or_welcome(message_data.message):
            assistant_content = generate_welcome_response(chat_id)
            sql_query = None
        else:
            # Generate SQL using LangChain
            sql_query = run_nl_to_sql(DEFAULT_DB_URI, message_data.message)
            assistant_content = f"Here's the SQL query for your request:\n\n```sql\n{sql_query}\n```\n\n💡 **Tip:** You can ask follow-up questions or request modifications to this query!"

        # Add assistant response
        assistant_message = Message(
            id=str(uuid.uuid4()),
            role="assistant",
            content=assistant_content,
            created_at=datetime.now()
        )

        messages_db[chat_id].append(assistant_message)

        # Update chat timestamp
        chats_db[chat_id].updated_at = datetime.now()

        return {
            "messages": [user_message, assistant_message],
            "sql_query": sql_query
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Database Integration Endpoints
@app.get("/database/tables")
def get_tables():
    """Get all available database tables"""
    try:
        tables = get_database_tables(DEFAULT_DB_URI)
        return tables
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tables: {str(e)}")

@app.get("/database/tables/{table_name}/schema")
def get_table_schema_endpoint(table_name: str):
    """Get schema for a specific table"""
    try:
        schema = get_table_schema(DEFAULT_DB_URI, table_name)
        return schema
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get table schema: {str(e)}")

@app.post("/database/query")
def execute_database_query(query_request: DatabaseQueryRequest):
    """Execute natural language query and return SQL + results"""
    try:
        sql_query = run_nl_to_sql(DEFAULT_DB_URI, query_request.query)

        return {
            "sql_query": sql_query,
            "natural_language_query": query_request.query,
            "selected_tables": query_request.selected_tables,
            "message": "SQL query generated successfully. Note: Actual execution is disabled for safety."
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Health check endpoint
@app.get("/")
def health_check():
    return {"status": "healthy", "message": "My SaaS Backend is running"}

# Original endpoint (keeping for backward compatibility)
@app.post("/query-text-to-sql/")
def query_text_to_sql(payload: QueryRequest):
    try:
        sql = run_nl_to_sql(payload.db_uri, payload.question)
        return {"sql_query": sql}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
