"""
Database models for the SaaS application.

This module defines all SQLAlchemy models for the application including:
- User management
- Chat sessions
- Messages
- Database connections
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid

Base = declarative_base()


class User(Base):
    """User model for authentication and user management."""
    
    __tablename__ = "users"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=True)
    full_name = Column(String(255), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    chats = relationship("Chat", back_populates="user", cascade="all, delete-orphan")
    database_connections = relationship("DatabaseConnection", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email})>"


class DatabaseConnection(Base):
    """Model for storing user's database connection configurations."""
    
    __tablename__ = "database_connections"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)  # User-friendly name for the connection
    db_type = Column(String(50), nullable=False)  # mysql, postgresql, sqlite, etc.
    host = Column(String(255), nullable=True)
    port = Column(Integer, nullable=True)
    database_name = Column(String(255), nullable=False)
    username = Column(String(255), nullable=True)
    password_encrypted = Column(Text, nullable=True)  # Encrypted password
    connection_string = Column(Text, nullable=True)  # Full connection string (encrypted)
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_tested = Column(DateTime(timezone=True), nullable=True)
    
    # Additional metadata
    metadata_info = Column(JSON, nullable=True)  # Store table schemas, etc.
    
    # Relationships
    user = relationship("User", back_populates="database_connections")
    chats = relationship("Chat", back_populates="database_connection")
    
    def __repr__(self):
        return f"<DatabaseConnection(id={self.id}, name={self.name}, db_type={self.db_type})>"


class Chat(Base):
    """Chat session model."""
    
    __tablename__ = "chats"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    database_connection_id = Column(String(36), ForeignKey("database_connections.id"), nullable=True)
    title = Column(String(500), nullable=False, default="New Chat")
    description = Column(Text, nullable=True)
    is_archived = Column(Boolean, default=False)
    is_shared = Column(Boolean, default=False)
    share_token = Column(String(255), nullable=True, unique=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Chat settings
    settings = Column(JSON, nullable=True)  # Store chat-specific settings
    
    # Relationships
    user = relationship("User", back_populates="chats")
    database_connection = relationship("DatabaseConnection", back_populates="chats")
    messages = relationship("Message", back_populates="chat", cascade="all, delete-orphan", order_by="Message.created_at")
    
    def __repr__(self):
        return f"<Chat(id={self.id}, title={self.title}, user_id={self.user_id})>"


class Message(Base):
    """Message model for chat conversations."""
    
    __tablename__ = "messages"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    chat_id = Column(String(36), ForeignKey("chats.id"), nullable=False)
    role = Column(String(20), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # SQL-specific fields
    sql_query = Column(Text, nullable=True)  # Generated SQL query
    query_result = Column(JSON, nullable=True)  # Query execution results
    execution_time = Column(Integer, nullable=True)  # Execution time in milliseconds
    error_message = Column(Text, nullable=True)  # Error if query failed
    
    # Message metadata
    metadata_info = Column(JSON, nullable=True)  # Additional metadata
    
    # Relationships
    chat = relationship("Chat", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, role={self.role}, chat_id={self.chat_id})>"


class Tenant(Base):
    """Tenant model for multi-tenant architecture."""

    __tablename__ = "tenants"

    id = Column(Integer, primary_key=True, autoincrement=True)
    employer_name = Column(String(255), nullable=False, index=True)
    size = Column(String(50), nullable=True)  # e.g., 'small', 'medium', 'large', '1-10', '11-50', etc.
    email = Column(String(255), nullable=False, index=True)
    phone = Column(String(20), nullable=True)  # Phone number for the tenant/company
    status = Column(String(20), nullable=False, default='active')  # 'active', 'inactive'
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    tenant_users = relationship("TenantUser", back_populates="tenant", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Tenant(id={self.id}, employer_name={self.employer_name})>"


class TenantUser(Base):
    """Tenant user model for users within a tenant organization."""

    __tablename__ = "tenant_users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False)  # 'admin', 'analyst', 'consumer', etc.
    email = Column(String(255), nullable=False, index=True)
    phone = Column(String(20), nullable=True)  # Phone number for the user
    status = Column(String(20), nullable=False, default='active')  # 'active', 'inactive'
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    tenant = relationship("Tenant", back_populates="tenant_users")

    def __repr__(self):
        return f"<TenantUser(id={self.id}, name={self.name}, role={self.role}, tenant_id={self.tenant_id})>"


class AdminUser(Base):
    """Admin user model for system administrators."""

    __tablename__ = "admin_users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    phone = Column(String(20), nullable=True)  # Phone number for the admin user
    role = Column(String(50), nullable=False)  # 'super_admin', 'admin', 'moderator', etc.
    email = Column(String(255), nullable=False, unique=True, index=True)
    password = Column(String(255), nullable=False)  # Encrypted password for login
    status = Column(String(20), nullable=False, default='active')  # 'active', 'inactive'
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AdminUser(id={self.id}, name={self.name}, role={self.role}, status={self.status})>"


class QueryHistory(Base):
    """Model for tracking query history and analytics."""

    __tablename__ = "query_history"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    database_connection_id = Column(String(36), ForeignKey("database_connections.id"), nullable=True)
    natural_language_query = Column(Text, nullable=False)
    generated_sql = Column(Text, nullable=True)
    execution_status = Column(String(20), nullable=False)  # 'success', 'error', 'timeout'
    execution_time = Column(Integer, nullable=True)  # in milliseconds
    rows_affected = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Analytics fields
    query_complexity = Column(String(20), nullable=True)  # 'simple', 'medium', 'complex'
    tables_involved = Column(JSON, nullable=True)  # List of tables used
    
    # Relationships
    user = relationship("User")
    database_connection = relationship("DatabaseConnection")
    
    def __repr__(self):
        return f"<QueryHistory(id={self.id}, status={self.execution_status})>"


class APIKey(Base):
    """Model for managing API keys for external services."""
    
    __tablename__ = "api_keys"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    service_name = Column(String(100), nullable=False)  # 'openai', 'anthropic', etc.
    key_name = Column(String(255), nullable=False)  # User-friendly name
    encrypted_key = Column(Text, nullable=False)  # Encrypted API key
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_used = Column(DateTime(timezone=True), nullable=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    monthly_usage = Column(JSON, nullable=True)  # Track monthly usage
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, service={self.service_name}, user_id={self.user_id})>"
