{% extends "base.html" %}

{% block title %}Admin Users - Admin{% endblock %}
{% block page_title %}Admin Users{% endblock %}

{% block page_actions %}
<a href="/admin/admin-users/new" class="btn btn-primary">
    <i class="bi bi-shield-plus me-2"></i>
    Add Admin User
</a>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Main Content -->
    <div class="col-lg-9">
        <!-- Admin Users Table -->
        <div class="card shadow-sm">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    Admin Users ({{ admin_users|length }} found)
                </h6>
            </div>
            <div class="card-body p-0">
                {% if admin_users %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>User</th>
                                    <th>Contact</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th class="table-actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in admin_users %}
                                <tr>
                                    <td>
                                        <code class="text-muted small">{{ user.id }}</code>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm rounded-circle bg-danger text-white me-3">
                                                {{ user.name[0].upper() }}
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ user.name }}</div>
                                                <div class="text-muted small">{{ user.email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="bi bi-envelope me-1"></i>{{ user.email }}
                                        </div>
                                        {% if user.phone %}
                                        <div class="text-muted small">
                                            <i class="bi bi-telephone me-1"></i>{{ user.phone }}
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if user.role == 'super_admin' %}danger{% elif user.role == 'admin' %}warning{% else %}info{% endif %}">
                                            <i class="bi bi-shield-check me-1"></i>{{ user.role.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if user.status == 'active' %}success{% else %}secondary{% endif %}">
                                            <i class="bi bi-{% if user.status == 'active' %}check-circle{% else %}x-circle{% endif %} me-1"></i>{{ user.status.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'N/A' }}</div>
                                        <div class="text-muted small">{{ user.created_at.strftime('%H:%M') if user.created_at else '' }}</div>
                                    </td>
                                    <td class="table-actions">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/admin/admin-users/{{ user.id }}/edit" class="btn btn-outline-secondary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            {% if user.status == 'active' %}
                                                <form method="POST" action="/admin/admin-users/{{ user.id }}/deactivate" class="d-inline">
                                                    <button type="submit" class="btn btn-outline-warning" title="Deactivate">
                                                        <i class="bi bi-pause-circle"></i>
                                                    </button>
                                                </form>
                                            {% else %}
                                                <form method="POST" action="/admin/admin-users/{{ user.id }}/activate" class="d-inline">
                                                    <button type="submit" class="btn btn-outline-success" title="Activate">
                                                        <i class="bi bi-play-circle"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                            <form method="POST" action="/admin/admin-users/{{ user.id }}/delete" class="d-inline" 
                                                  onsubmit="return confirmDelete('{{ user.name }}')">
                                                <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-shield-check text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">No admin users found</h5>
                        <p class="text-muted">
                            {% if request.query_params.get('search') or request.query_params.get('role') or request.query_params.get('status') %}
                                Try adjusting your search criteria or filters.
                            {% else %}
                                Get started by creating your first admin user.
                            {% endif %}
                        </p>
                        {% if not request.query_params.get('search') and not request.query_params.get('role') and not request.query_params.get('status') %}
                            <a href="/admin/admin-users/new" class="btn btn-primary">
                                <i class="bi bi-shield-plus me-2"></i>
                                Create First Admin User
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Total Count -->
        <div class="mt-3 text-center">
            <small class="text-muted">Total: {{ admin_users|length }} admin user{{ 's' if admin_users|length != 1 else '' }}</small>
        </div>
    </div>

    <!-- Filters Sidebar -->
    <div class="col-lg-3">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
            </div>
            <div class="card-body">
                <form method="GET">
                    <div class="mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.query_params.get('search', '') }}" 
                               placeholder="Name or email...">
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role">
                            <option value="">All Roles</option>
                            <option value="super_admin" {% if request.query_params.get('role') == 'super_admin' %}selected{% endif %}>Super Admin</option>
                            <option value="admin" {% if request.query_params.get('role') == 'admin' %}selected{% endif %}>Admin</option>
                            <option value="moderator" {% if request.query_params.get('role') == 'moderator' %}selected{% endif %}>Moderator</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {% if request.query_params.get('status') == 'active' %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if request.query_params.get('status') == 'inactive' %}selected{% endif %}>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Apply Filters</button>
                        <a href="/admin/admin-users" class="btn btn-outline-secondary">Clear</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
}
</style>
{% endblock %}
