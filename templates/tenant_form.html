{% extends "base.html" %}

{% block title %}{{ 'Edit' if tenant else 'Create' }} Tenant - Admin{% endblock %}
{% block page_title %}{{ 'Edit' if tenant else 'Create' }} Tenant{% endblock %}

{% block page_actions %}
<a href="/admin/tenants" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Tenants
</a>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'Edit Tenant Information' if tenant else 'Create New Tenant' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="employer_name" class="form-label">
                                Company Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control {% if errors.employer_name %}is-invalid{% endif %}" 
                                   id="employer_name" 
                                   name="employer_name" 
                                   value="{{ tenant.employer_name if tenant else form_data.get('employer_name', '') }}"
                                   required>
                            {% if errors.employer_name %}
                                <div class="invalid-feedback">{{ errors.employer_name }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                Email Address <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control {% if errors.email %}is-invalid{% endif %}" 
                                   id="email" 
                                   name="email" 
                                   value="{{ tenant.email if tenant else form_data.get('email', '') }}"
                                   required>
                            {% if errors.email %}
                                <div class="invalid-feedback">{{ errors.email }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" 
                                   class="form-control {% if errors.phone %}is-invalid{% endif %}" 
                                   id="phone" 
                                   name="phone" 
                                   value="{{ tenant.phone if tenant else form_data.get('phone', '') }}"
                                   placeholder="******-123-4567">
                            {% if errors.phone %}
                                <div class="invalid-feedback">{{ errors.phone }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="size" class="form-label">Company Size</label>
                            <select class="form-select {% if errors.size %}is-invalid{% endif %}" 
                                    id="size" 
                                    name="size">
                                <option value="">Select company size</option>
                                <option value="small" 
                                        {% if (tenant and tenant.size == 'small') or form_data.get('size') == 'small' %}selected{% endif %}>
                                    Small (1-10 employees)
                                </option>
                                <option value="medium" 
                                        {% if (tenant and tenant.size == 'medium') or form_data.get('size') == 'medium' %}selected{% endif %}>
                                    Medium (11-50 employees)
                                </option>
                                <option value="large" 
                                        {% if (tenant and tenant.size == 'large') or form_data.get('size') == 'large' %}selected{% endif %}>
                                    Large (50+ employees)
                                </option>
                            </select>
                            {% if errors.size %}
                                <div class="invalid-feedback">{{ errors.size }}</div>
                            {% endif %}
                        </div>
                    </div>

                    {% if tenant %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Created</label>
                            <input type="text" class="form-control" 
                                   value="{{ tenant.created_at.strftime('%Y-%m-%d %H:%M:%S') if tenant.created_at else 'N/A' }}" 
                                   readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Last Updated</label>
                            <input type="text" class="form-control" 
                                   value="{{ tenant.updated_at.strftime('%Y-%m-%d %H:%M:%S') if tenant.updated_at else 'N/A' }}" 
                                   readonly>
                        </div>
                    </div>
                    {% endif %}

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="/admin/tenants" class="btn btn-outline-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            {{ 'Update Tenant' if tenant else 'Create Tenant' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        {% if tenant %}
        <!-- Tenant Users Section -->
        <div class="card shadow-sm mt-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    Users ({{ tenant_users|length }})
                </h6>
                <a href="/admin/users/new?tenant_id={{ tenant.id }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-person-plus me-1"></i>
                    Add User
                </a>
            </div>
            <div class="card-body p-0">
                {% if tenant_users %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Phone</th>
                                    <th class="table-actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in tenant_users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm rounded-circle bg-success text-white me-2">
                                                {{ user.name[0].upper() }}
                                            </div>
                                            {{ user.name }}
                                        </div>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge badge-role bg-{% if user.role == 'admin' %}danger{% elif user.role == 'analyst' %}primary{% else %}success{% endif %}">
                                            {{ user.role.title() }}
                                        </span>
                                    </td>
                                    <td>{{ user.phone or '-' }}</td>
                                    <td class="table-actions">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/admin/users/{{ user.id }}/edit" class="btn btn-outline-secondary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <form method="POST" action="/admin/users/{{ user.id }}/delete" class="d-inline" 
                                                  onsubmit="return confirmDelete('{{ user.name }}')">
                                                <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No users found for this tenant</p>
                        <a href="/admin/users/new?tenant_id={{ tenant.id }}" class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>
                            Add First User
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.75rem;
}
</style>
{% endblock %}
