{% extends "base.html" %}

{% block title %}Tenants - Admin{% endblock %}
{% block page_title %}Tenants{% endblock %}

{% block page_actions %}
<a href="/admin/tenants/new" class="btn btn-primary">
    <i class="bi bi-plus-circle me-2"></i>
    Add Tenant
</a>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Main Content -->
    <div class="col-lg-9">
        <!-- Tenants Table -->
        <div class="card shadow-sm">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    Tenants ({{ tenants|length }} found)
                </h6>
            </div>
            <div class="card-body p-0">
                {% if tenants %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Company</th>
                                    <th>Contact</th>
                                    <th>Size</th>
                                    <th>Status</th>
                                    <th>Users</th>
                                    <th>Created</th>
                                    <th class="table-actions">Actions</th>
                                </tr>
                            </thead>
                    <tbody>
                        {% for tenant in tenants %}
                        <tr>
                            <td>
                                <code class="text-muted small">{{ tenant.id }}</code>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm rounded-circle bg-primary text-white me-3">
                                        {{ tenant.employer_name[0].upper() }}
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ tenant.employer_name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <i class="bi bi-envelope me-1"></i>{{ tenant.email }}
                                </div>
                                {% if tenant.phone %}
                                <div class="text-muted small">
                                    <i class="bi bi-telephone me-1"></i>{{ tenant.phone }}
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                {% if tenant.size %}
                                    <span class="badge bg-{% if tenant.size == 'large' %}success{% elif tenant.size == 'medium' %}warning{% else %}info{% endif %}">
                                        {{ tenant.size.title() }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">Unknown</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{% if tenant.status == 'active' %}success{% else %}secondary{% endif %}">
                                    <i class="bi bi-{% if tenant.status == 'active' %}check-circle{% else %}x-circle{% endif %} me-1"></i>{{ tenant.status.title() }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ tenant.user_count or 0 }}</span>
                            </td>
                            <td>
                                <div>{{ tenant.created_at.strftime('%Y-%m-%d') if tenant.created_at else 'N/A' }}</div>
                                <div class="text-muted small">{{ tenant.created_at.strftime('%H:%M') if tenant.created_at else '' }}</div>
                            </td>
                            <td class="table-actions">
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="/admin/tenants/{{ tenant.id }}" class="btn btn-outline-primary" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="/admin/tenants/{{ tenant.id }}/edit" class="btn btn-outline-secondary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <form method="POST" action="/admin/tenants/{{ tenant.id }}/delete" class="d-inline" 
                                          onsubmit="return confirmDelete('{{ tenant.employer_name }}')">
                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                <h5 class="mt-3 text-muted">No tenants found</h5>
                <p class="text-muted">
                    {% if request.query_params.get('search') or request.query_params.get('size') %}
                        Try adjusting your search criteria or filters.
                    {% else %}
                        Get started by creating your first tenant.
                    {% endif %}
                </p>
                {% if not request.query_params.get('search') and not request.query_params.get('size') %}
                    <a href="/admin/tenants/new" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Create First Tenant
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

        <!-- Total Count -->
        <div class="mt-3 text-center">
            <small class="text-muted">Total: {{ tenants|length }} tenant{{ 's' if tenants|length != 1 else '' }}</small>
        </div>
    </div>

    <!-- Filters Sidebar -->
    <div class="col-lg-3">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
            </div>
            <div class="card-body">
                <form method="GET">
                    <div class="mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request.query_params.get('search', '') }}"
                               placeholder="Company name or email...">
                    </div>

                    <div class="mb-3">
                        <label for="size" class="form-label">Company Size</label>
                        <select class="form-select" id="size" name="size">
                            <option value="">All Sizes</option>
                            <option value="small" {% if request.query_params.get('size') == 'small' %}selected{% endif %}>Small</option>
                            <option value="medium" {% if request.query_params.get('size') == 'medium' %}selected{% endif %}>Medium</option>
                            <option value="large" {% if request.query_params.get('size') == 'large' %}selected{% endif %}>Large</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {% if request.query_params.get('status') == 'active' %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if request.query_params.get('status') == 'inactive' %}selected{% endif %}>Inactive</option>
                        </select>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Apply Filters</button>
                        <a href="/admin/tenants" class="btn btn-outline-secondary">Clear</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
}
</style>
{% endblock %}
