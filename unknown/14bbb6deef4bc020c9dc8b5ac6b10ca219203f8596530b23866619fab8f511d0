#!/usr/bin/env python3
"""
Database utilities for schema inspection and management.
"""

import sys
import os
from datetime import datetime

# Add the project root directory to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from database import engine
from sqlalchemy import text, inspect

def inspect_database_schema():
    """
    Inspect and return complete database schema information.
    """
    inspector = inspect(engine)
    schema_info = {
        'tables': {},
        'metadata': {
            'engine': str(engine.url),
            'dialect': engine.dialect.name,
            'generated_at': datetime.now().isoformat()
        }
    }
    
    # Get all table names
    table_names = inspector.get_table_names()
    
    for table_name in table_names:
        if table_name.startswith('alembic_'):
            continue  # Skip alembic internal tables
            
        table_info = {
            'columns': [],
            'indexes': [],
            'foreign_keys': [],
            'primary_keys': [],
            'row_count': 0
        }
        
        # Get columns
        columns = inspector.get_columns(table_name)
        for col in columns:
            table_info['columns'].append({
                'name': col['name'],
                'type': str(col['type']),
                'nullable': col['nullable'],
                'default': col['default'],
                'autoincrement': col.get('autoincrement', False),
                'primary_key': col.get('primary_key', False)
            })
        
        # Get indexes
        indexes = inspector.get_indexes(table_name)
        for idx in indexes:
            table_info['indexes'].append({
                'name': idx['name'],
                'columns': idx['column_names'],
                'unique': idx['unique']
            })
        
        # Get foreign keys
        foreign_keys = inspector.get_foreign_keys(table_name)
        for fk in foreign_keys:
            table_info['foreign_keys'].append({
                'name': fk['name'],
                'constrained_columns': fk['constrained_columns'],
                'referred_table': fk['referred_table'],
                'referred_columns': fk['referred_columns']
            })
        
        # Get primary keys
        pk = inspector.get_pk_constraint(table_name)
        table_info['primary_keys'] = pk['constrained_columns']
        
        # Get row count
        with engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            table_info['row_count'] = result.fetchone()[0]
        
        schema_info['tables'][table_name] = table_info
    
    return schema_info

def generate_schema_documentation():
    """
    Generate comprehensive schema documentation.
    """
    schema_info = inspect_database_schema()
    
    doc = []
    doc.append("# Database Schema Report")
    doc.append(f"Generated: {schema_info['metadata']['generated_at']}")
    doc.append(f"Engine: {schema_info['metadata']['engine']}")
    doc.append(f"Dialect: {schema_info['metadata']['dialect']}")
    doc.append("")
    
    # Table summary
    doc.append("## Table Summary")
    doc.append("")
    doc.append("| Table | Columns | Rows | Primary Key | Foreign Keys |")
    doc.append("|-------|---------|------|-------------|--------------|")
    
    for table_name, table_info in schema_info['tables'].items():
        col_count = len(table_info['columns'])
        row_count = table_info['row_count']
        pk = ', '.join(table_info['primary_keys'])
        fk_count = len(table_info['foreign_keys'])
        
        doc.append(f"| {table_name} | {col_count} | {row_count} | {pk} | {fk_count} |")
    
    doc.append("")
    
    # Detailed table information
    for table_name, table_info in schema_info['tables'].items():
        doc.append(f"## Table: {table_name}")
        doc.append("")
        doc.append(f"**Row Count**: {table_info['row_count']}")
        doc.append("")
        
        # Columns
        doc.append("### Columns")
        doc.append("")
        doc.append("| Name | Type | Nullable | Default | Auto Inc | Primary Key |")
        doc.append("|------|------|----------|---------|----------|-------------|")
        
        for col in table_info['columns']:
            nullable = "Yes" if col['nullable'] else "No"
            default = str(col['default']) if col['default'] is not None else ""
            auto_inc = "Yes" if col['autoincrement'] else "No"
            pk = "Yes" if col['primary_key'] else "No"
            
            doc.append(f"| {col['name']} | {col['type']} | {nullable} | {default} | {auto_inc} | {pk} |")
        
        doc.append("")
        
        # Foreign Keys
        if table_info['foreign_keys']:
            doc.append("### Foreign Keys")
            doc.append("")
            for fk in table_info['foreign_keys']:
                constrained = ', '.join(fk['constrained_columns'])
                referred = ', '.join(fk['referred_columns'])
                doc.append(f"- `{constrained}` → `{fk['referred_table']}.{referred}`")
            doc.append("")
        
        # Indexes
        if table_info['indexes']:
            doc.append("### Indexes")
            doc.append("")
            for idx in table_info['indexes']:
                columns = ', '.join(idx['columns'])
                unique = " (UNIQUE)" if idx['unique'] else ""
                doc.append(f"- `{idx['name']}` on `{columns}`{unique}")
            doc.append("")
        
        doc.append("---")
        doc.append("")
    
    return '\n'.join(doc)

def export_schema_to_sql():
    """
    Export current schema as SQL DDL statements.
    """
    with engine.connect() as conn:
        # Get schema for each table
        tables = []
        
        # Get all table names
        result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'alembic_%' ORDER BY name"))
        table_names = [row[0] for row in result.fetchall()]
        
        for table_name in table_names:
            # Get CREATE TABLE statement
            result = conn.execute(text(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'"))
            create_sql = result.fetchone()[0]
            tables.append(f"-- Table: {table_name}")
            tables.append(create_sql + ";")
            tables.append("")
            
            # Get indexes
            result = conn.execute(text(f"SELECT sql FROM sqlite_master WHERE type='index' AND tbl_name='{table_name}' AND sql IS NOT NULL"))
            for row in result.fetchall():
                if row[0]:
                    tables.append(row[0] + ";")
            tables.append("")
    
    return '\n'.join(tables)

def main():
    """
    Main function to generate and save schema documentation.
    """
    print("🔍 Generating database schema documentation...")
    
    try:
        # Generate documentation
        doc = generate_schema_documentation()
        
        # Save to file
        doc_path = os.path.join(os.path.dirname(__file__), 'schema_report.md')
        with open(doc_path, 'w') as f:
            f.write(doc)
        
        print(f"✅ Schema documentation saved to: {doc_path}")
        
        # Generate SQL export
        sql_export = export_schema_to_sql()
        sql_path = os.path.join(os.path.dirname(__file__), 'current_schema.sql')
        with open(sql_path, 'w') as f:
            f.write(f"-- Current Database Schema Export\n")
            f.write(f"-- Generated: {datetime.now().isoformat()}\n\n")
            f.write(sql_export)
        
        print(f"✅ SQL schema export saved to: {sql_path}")
        
        # Print summary
        schema_info = inspect_database_schema()
        print(f"\n📊 Schema Summary:")
        print(f"   Tables: {len(schema_info['tables'])}")
        total_rows = sum(table['row_count'] for table in schema_info['tables'].values())
        print(f"   Total Rows: {total_rows}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating schema documentation: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
